# Promodetect API

A comprehensive RESTful API for promotion detection and management, built with FastAPI and PostgreSQL. This system allows merchants to manage promotions, customers to discover deals, and administrators to oversee the platform.

## Features

### 🔐 Authentication & Authorization
- JWT-based authentication with access and refresh tokens
- Role-based access control (Ad<PERSON>, Merchant, Customer, Moderator)
- Secure password hashing with bcrypt
- Session management and tracking

### 👥 User Management
- User registration and email verification
- Profile management with multi-language support
- Password reset functionality
- Admin user management

### 🏪 Merchant Management
- Merchant profile creation and verification
- Multiple business locations support
- Business type categorization
- Merchant analytics and statistics

### 🎯 Promotion Management
- Comprehensive promotion CRUD operations
- Multiple promotion types (discounts, BOGO, cashback, etc.)
- Advanced search and filtering
- Geolocation-based promotion discovery
- Promotion analytics and tracking

### 📂 Category Management
- Hierarchical category structure
- Multi-language category names and descriptions
- Category-based promotion organization

### ⭐ User Engagement
- Promotion favorites system
- Review and rating system
- Notification management
- User feedback collection

### 📊 Analytics & Monitoring
- Comprehensive event tracking
- Daily statistics aggregation
- Performance metrics
- User behavior analytics

### 🌐 Multi-language Support
- English, French, and Arabic language support
- Localized content management
- Language-specific API responses

### 🔒 Security Features
- Rate limiting and API throttling
- Input validation and sanitization
- SQL injection prevention
- Security headers middleware
- Request size limiting

## Technology Stack

- **Framework**: FastAPI 0.104.1
- **Database**: PostgreSQL with SQLAlchemy ORM
- **Authentication**: JWT with python-jose
- **Caching**: Redis
- **Validation**: Pydantic v2
- **Migration**: Alembic
- **Testing**: Pytest
- **Documentation**: OpenAPI/Swagger
- **Containerization**: Docker & Docker Compose

## Quick Start

### Using Docker Compose (Recommended)

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd promodetect_api
   ```

2. **Copy environment file**
   ```bash
   cp .env.example .env
   ```

3. **Start the services**
   ```bash
   docker-compose up -d
   ```

4. **Run database migrations**
   ```bash
   docker-compose exec api alembic upgrade head
   ```

5. **Access the API**
   - API: http://localhost:8000
   - Documentation: http://localhost:8000/api/v1/docs
   - ReDoc: http://localhost:8000/api/v1/redoc

### Manual Installation

1. **Prerequisites**
   - Python 3.11+
   - PostgreSQL 12+
   - Redis 6+

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Setup environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your database and Redis configurations
   ```

4. **Run database migrations**
   ```bash
   alembic upgrade head
   ```

5. **Start the application**
   ```bash
   python run.py
   ```

## API Documentation

The API provides comprehensive OpenAPI documentation available at:
- **Swagger UI**: `/api/v1/docs`
- **ReDoc**: `/api/v1/redoc`
- **OpenAPI JSON**: `/api/v1/openapi.json`

### Main Endpoints

#### Authentication
- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/refresh` - Refresh access token
- `GET /api/v1/auth/me` - Get current user info

#### Users
- `GET /api/v1/users/profile` - Get user profile
- `PUT /api/v1/users/profile` - Update user profile
- `POST /api/v1/users/change-password` - Change password

#### Promotions (Coming Soon)
- `GET /api/v1/promotions` - List promotions
- `POST /api/v1/promotions` - Create promotion
- `GET /api/v1/promotions/{id}` - Get promotion details
- `PUT /api/v1/promotions/{id}` - Update promotion
- `DELETE /api/v1/promotions/{id}` - Delete promotion

## Database Schema

The application uses a comprehensive PostgreSQL schema with the following main entities:

- **Users**: User accounts with roles and profiles
- **Merchants**: Business profiles and locations
- **Categories**: Hierarchical promotion categories
- **Promotions**: Deals, discounts, and offers
- **Reviews**: User feedback and ratings
- **Notifications**: System and user notifications
- **Analytics**: Event tracking and statistics

## Configuration

Key environment variables:

```env
# Database
DATABASE_URL=postgresql://username:password@localhost:5432/promodetect_db

# JWT
SECRET_KEY=your-super-secret-jwt-key
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Redis
REDIS_URL=redis://localhost:6379/0

# API
DEBUG=True
ENVIRONMENT=development
```

## Development

### Running Tests
```bash
pytest
```

### Code Formatting
```bash
black app/
isort app/
```

### Database Migrations
```bash
# Create migration
alembic revision --autogenerate -m "Description"

# Apply migrations
alembic upgrade head

# Downgrade
alembic downgrade -1
```

## Deployment

### Production Deployment with Docker

1. **Update environment variables**
   ```bash
   # Set production values in .env
   DEBUG=False
   ENVIRONMENT=production
   SECRET_KEY=<strong-secret-key>
   ```

2. **Deploy with Docker Compose**
   ```bash
   docker-compose -f docker-compose.yml --profile production up -d
   ```

### Health Checks

The application provides health check endpoints:
- `GET /health` - Basic health check
- `GET /api/v1/info` - API information

## Security

- JWT tokens with configurable expiration
- Password strength validation
- Rate limiting (60 requests/minute by default)
- CORS protection
- Security headers middleware
- Input validation and sanitization
- SQL injection prevention

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run tests and ensure they pass
6. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support and questions:
- Create an issue in the repository
- Contact the development team

## Project Status

✅ **Completed Features:**
- User authentication and authorization (JWT)
- User management with role-based access control
- Promotion CRUD operations with advanced search
- File upload system for images
- Favorites system for users
- Notification system with templates
- Multi-language support (EN, FR, AR)
- Rate limiting and security middleware
- Comprehensive API documentation
- Database models and migrations
- Docker containerization
- Basic test suite

🚧 **In Progress:**
- Merchant management endpoints
- Category management system
- Review and rating system
- Analytics dashboard

📋 **Roadmap:**
- [ ] Complete merchant management endpoints
- [ ] Advanced search and filtering
- [ ] Real-time notifications with WebSocket
- [ ] Mobile app API support
- [ ] Third-party integrations (payment gateways)
- [ ] Advanced analytics dashboard
- [ ] Multi-tenant support
- [ ] Performance optimization
- [ ] Comprehensive monitoring and alerting

## API Endpoints Summary

### Authentication
- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/refresh` - Refresh access token
- `POST /api/v1/auth/logout` - User logout
- `GET /api/v1/auth/me` - Get current user info

### Users
- `GET /api/v1/users/profile` - Get user profile
- `PUT /api/v1/users/profile` - Update user profile
- `POST /api/v1/users/change-password` - Change password
- `GET /api/v1/users/{id}` - Get user by ID
- `GET /api/v1/users/` - List users (Admin)
- `PUT /api/v1/users/admin/{id}` - Update user (Admin)
- `DELETE /api/v1/users/admin/{id}` - Delete user (Admin)

### Promotions
- `GET /api/v1/promotions/` - List promotions
- `POST /api/v1/promotions/` - Create promotion (Merchant)
- `GET /api/v1/promotions/{id}` - Get promotion details
- `PUT /api/v1/promotions/{id}` - Update promotion (Merchant)
- `DELETE /api/v1/promotions/{id}` - Delete promotion (Merchant)
- `POST /api/v1/promotions/{id}/click` - Track promotion click
- `GET /api/v1/promotions/merchant/my-promotions` - Get merchant's promotions
- `GET /api/v1/promotions/merchant/stats` - Get promotion statistics

### File Upload
- `POST /api/v1/files/upload/avatar` - Upload user avatar
- `POST /api/v1/files/upload/promotion-image` - Upload promotion image
- `POST /api/v1/files/upload/promotion-gallery` - Upload promotion gallery
- `POST /api/v1/files/upload/merchant-logo` - Upload merchant logo
- `POST /api/v1/files/upload/merchant-cover` - Upload merchant cover
- `DELETE /api/v1/files/delete/{path}` - Delete file
- `GET /api/v1/files/serve/{path}` - Serve uploaded files

### Favorites
- `POST /api/v1/favorites/{promotion_id}` - Add to favorites
- `DELETE /api/v1/favorites/{promotion_id}` - Remove from favorites
- `GET /api/v1/favorites/` - Get user's favorites
- `GET /api/v1/favorites/check/{promotion_id}` - Check favorite status
- `POST /api/v1/favorites/check-multiple` - Check multiple favorites
- `GET /api/v1/favorites/count` - Get favorites count
- `GET /api/v1/favorites/popular` - Get popular promotions

### Notifications
- `GET /api/v1/notifications/` - Get user notifications
- `GET /api/v1/notifications/unread-count` - Get unread count
- `POST /api/v1/notifications/{id}/read` - Mark as read
- `POST /api/v1/notifications/mark-all-read` - Mark all as read
- `DELETE /api/v1/notifications/{id}` - Delete notification
- `GET /api/v1/notifications/{id}` - Get notification details
- `POST /api/v1/notifications/admin/broadcast` - Broadcast notification (Admin)

## Database Schema

The application uses a comprehensive PostgreSQL schema with the following entities:

### Core Entities
- **Users**: User accounts with authentication and profiles
- **Merchants**: Business profiles with locations and verification
- **Categories**: Hierarchical promotion categories with multi-language support
- **Promotions**: Deals and offers with rich metadata and analytics
- **UserFavorites**: User's saved promotions
- **Reviews**: User feedback and ratings for promotions/merchants
- **Notifications**: User notification system with templates
- **Analytics**: Event tracking and performance metrics

### Key Features
- Multi-language support for content
- Soft delete for data integrity
- Comprehensive audit trails
- Optimized indexes for performance
- Foreign key constraints for data consistency
