# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/promodetect_db
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=promodetect_db
DATABASE_USER=username
DATABASE_PASSWORD=password

# JWT Configuration
SECRET_KEY=your-super-secret-jwt-key-here-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# API Configuration
API_V1_STR=/api/v1
PROJECT_NAME=Promodetect API
PROJECT_VERSION=1.0.0
DEBUG=True
ENVIRONMENT=development

# CORS Configuration
BACKEND_CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080"]

# Redis Configuration (for caching and rate limiting)
REDIS_URL=redis://localhost:6379/0

# File Upload Configuration
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760  # 10MB
ALLOWED_IMAGE_EXTENSIONS=jpg,jpeg,png,gif,webp

# Email Configuration
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-email-password
MAIL_FROM=<EMAIL>
MAIL_PORT=587
MAIL_SERVER=smtp.gmail.com
MAIL_TLS=True
MAIL_SSL=False

# External APIs
GOOGLE_MAPS_API_KEY=your-google-maps-api-key

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json

# Celery Configuration (for background tasks)
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/1

# Internationalization
DEFAULT_LANGUAGE=en
SUPPORTED_LANGUAGES=en,fr,ar
