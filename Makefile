# Promodetect API Makefile

.PHONY: help install dev test lint format clean docker-build docker-run docker-stop migrate init-db

# Default target
help:
	@echo "Available commands:"
	@echo "  install     - Install dependencies"
	@echo "  dev         - Run development server"
	@echo "  test        - Run tests"
	@echo "  lint        - Run linting"
	@echo "  format      - Format code"
	@echo "  clean       - Clean up temporary files"
	@echo "  docker-build - Build Docker image"
	@echo "  docker-run  - Run with Docker Compose"
	@echo "  docker-stop - Stop Docker Compose"
	@echo "  migrate     - Run database migrations"
	@echo "  init-db     - Initialize database with default data"

# Install dependencies
install:
	pip install -r requirements.txt

# Run development server
dev:
	python run.py

# Run tests
test:
	pytest -v

# Run tests with coverage
test-coverage:
	pytest --cov=app --cov-report=html --cov-report=term

# Run linting
lint:
	flake8 app/
	black --check app/
	isort --check-only app/

# Format code
format:
	black app/
	isort app/

# Clean up temporary files
clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	rm -rf .pytest_cache/
	rm -rf htmlcov/
	rm -rf dist/
	rm -rf build/

# Docker commands
docker-build:
	docker build -t promodetect-api .

docker-run:
	docker-compose up -d

docker-stop:
	docker-compose down

docker-logs:
	docker-compose logs -f

# Database commands
migrate:
	alembic upgrade head

migrate-create:
	alembic revision --autogenerate -m "$(MSG)"

migrate-downgrade:
	alembic downgrade -1

init-db:
	python scripts/init_db.py

# Development setup
setup-dev: install init-db
	@echo "Development environment setup complete!"

# Production deployment
deploy-prod:
	docker-compose -f docker-compose.yml --profile production up -d

# Backup database
backup-db:
	docker-compose exec db pg_dump -U postgres promodetect_db > backup_$(shell date +%Y%m%d_%H%M%S).sql

# Restore database
restore-db:
	docker-compose exec -T db psql -U postgres promodetect_db < $(FILE)

# Generate requirements.txt from current environment
freeze:
	pip freeze > requirements.txt

# Security scan
security-scan:
	bandit -r app/

# Check for outdated packages
check-outdated:
	pip list --outdated

# Run all quality checks
quality: lint test security-scan
	@echo "All quality checks passed!"

# Start development environment
start-dev: docker-run migrate init-db
	@echo "Development environment started!"
	@echo "API: http://localhost:8000"
	@echo "Docs: http://localhost:8000/api/v1/docs"

# Stop development environment
stop-dev: docker-stop clean
	@echo "Development environment stopped!"

# Reset development environment
reset-dev: docker-stop clean docker-run migrate init-db
	@echo "Development environment reset!"

# Production health check
health-check:
	curl -f http://localhost:8000/health || exit 1

# Load test data
load-test-data:
	python scripts/load_test_data.py

# Generate API client
generate-client:
	openapi-generator-cli generate -i http://localhost:8000/api/v1/openapi.json -g python -o client/

# Update dependencies
update-deps:
	pip install --upgrade pip
	pip install --upgrade -r requirements.txt

# Create migration
create-migration:
	@read -p "Enter migration message: " msg; \
	alembic revision --autogenerate -m "$$msg"

# Show database status
db-status:
	alembic current
	alembic history

# Tail logs
logs:
	tail -f logs/app.log

# Monitor system resources
monitor:
	docker stats

# Run performance tests
perf-test:
	locust -f tests/performance/locustfile.py --host=http://localhost:8000
