"""
Notification management endpoints.
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel

from app.core.database import get_db
from app.core.deps import get_current_user, get_current_admin_user
from app.services.notification_service import NotificationService
from app.models.user import User
from app.models.notification import NotificationType, NotificationChannel, NotificationPriority

router = APIRouter()


class NotificationResponse(BaseModel):
    """Notification response schema."""
    id: int
    title: str
    message: str
    notification_type: NotificationType
    priority: NotificationPriority
    is_read: bool
    action_url: Optional[str] = None
    action_text: Optional[str] = None
    created_at: str
    read_at: Optional[str] = None
    
    class Config:
        from_attributes = True


class NotificationList(BaseModel):
    """Notification list response schema."""
    notifications: List[NotificationResponse]
    total: int
    unread_count: int
    page: int
    per_page: int
    pages: int


class BroadcastNotification(BaseModel):
    """Broadcast notification schema."""
    title: str
    message: str
    notification_type: NotificationType
    priority: NotificationPriority = NotificationPriority.NORMAL
    channels: List[NotificationChannel] = [NotificationChannel.IN_APP]


@router.get("/", response_model=NotificationList)
async def get_notifications(
    page: int = Query(1, ge=1),
    per_page: int = Query(20, ge=1, le=100),
    unread_only: bool = Query(False),
    notification_type: Optional[NotificationType] = Query(None),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get user's notifications.
    """
    notification_service = NotificationService(db)
    
    skip = (page - 1) * per_page
    notifications, total = notification_service.get_user_notifications(
        user_id=current_user.id,
        unread_only=unread_only,
        notification_type=notification_type,
        skip=skip,
        limit=per_page
    )
    
    # Get unread count
    unread_count = notification_service.get_unread_count(current_user.id)
    
    pages = (total + per_page - 1) // per_page
    
    return NotificationList(
        notifications=notifications,
        total=total,
        unread_count=unread_count,
        page=page,
        per_page=per_page,
        pages=pages
    )


@router.get("/unread-count")
async def get_unread_count(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get count of unread notifications.
    """
    notification_service = NotificationService(db)
    count = notification_service.get_unread_count(current_user.id)
    
    return {
        "unread_count": count
    }


@router.post("/{notification_id}/read")
async def mark_notification_as_read(
    notification_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Mark a notification as read.
    """
    notification_service = NotificationService(db)
    success = notification_service.mark_as_read(notification_id, current_user.id)
    
    if success:
        return {"message": "Notification marked as read"}
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Failed to mark notification as read"
        )


@router.post("/mark-all-read")
async def mark_all_notifications_as_read(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Mark all notifications as read.
    """
    notification_service = NotificationService(db)
    count = notification_service.mark_all_as_read(current_user.id)
    
    return {
        "message": f"{count} notifications marked as read",
        "count": count
    }


@router.delete("/{notification_id}")
async def delete_notification(
    notification_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Delete a notification.
    """
    notification_service = NotificationService(db)
    success = notification_service.delete_notification(notification_id, current_user.id)
    
    if success:
        return {"message": "Notification deleted"}
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Failed to delete notification"
        )


@router.get("/{notification_id}", response_model=NotificationResponse)
async def get_notification(
    notification_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get a specific notification.
    """
    notification_service = NotificationService(db)
    notification = notification_service.get_by_id(notification_id)
    
    if not notification or notification.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Notification not found"
        )
    
    return notification


# Admin endpoints
@router.post("/admin/broadcast")
async def broadcast_notification(
    notification_data: BroadcastNotification,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    Broadcast notification to all users (Admin only).
    """
    notification_service = NotificationService(db)
    
    count = notification_service.broadcast_notification(
        title=notification_data.title,
        message=notification_data.message,
        notification_type=notification_data.notification_type,
        channels=notification_data.channels,
        priority=notification_data.priority
    )
    
    return {
        "message": f"Notification sent to {count} users",
        "count": count
    }


@router.post("/admin/send-to-user/{user_id}")
async def send_notification_to_user(
    user_id: int,
    notification_data: BroadcastNotification,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    Send notification to specific user (Admin only).
    """
    notification_service = NotificationService(db)
    
    notification = notification_service.create_notification(
        user_id=user_id,
        title=notification_data.title,
        message=notification_data.message,
        notification_type=notification_data.notification_type,
        channels=notification_data.channels,
        priority=notification_data.priority
    )
    
    return {
        "message": "Notification sent successfully",
        "notification_id": notification.id
    }
