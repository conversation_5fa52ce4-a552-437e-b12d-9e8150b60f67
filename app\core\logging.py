"""
Logging configuration for the application.
"""
import logging
import logging.config
import sys
from typing import Dict, Any
import structlog
from pythonjsonlogger import jsonlogger

from app.core.config import settings


def setup_logging() -> None:
    """Setup application logging configuration."""
    
    # Configure standard library logging
    logging_config = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "json": {
                "()": jsonlogger.JsonFormatter,
                "format": "%(asctime)s %(name)s %(levelname)s %(message)s"
            },
            "console": {
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            }
        },
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "level": settings.LOG_LEVEL,
                "formatter": "json" if settings.LOG_FORMAT == "json" else "console",
                "stream": sys.stdout
            },
            "file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": settings.LOG_LEVEL,
                "formatter": "json",
                "filename": "logs/app.log",
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5
            }
        },
        "loggers": {
            "app": {
                "level": settings.LOG_LEVEL,
                "handlers": ["console", "file"],
                "propagate": False
            },
            "uvicorn": {
                "level": "INFO",
                "handlers": ["console"],
                "propagate": False
            },
            "sqlalchemy.engine": {
                "level": "WARNING",
                "handlers": ["console"],
                "propagate": False
            }
        },
        "root": {
            "level": settings.LOG_LEVEL,
            "handlers": ["console"]
        }
    }
    
    # Create logs directory if it doesn't exist
    import os
    os.makedirs("logs", exist_ok=True)
    
    # Apply logging configuration
    logging.config.dictConfig(logging_config)
    
    # Configure structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer() if settings.LOG_FORMAT == "json" 
            else structlog.dev.ConsoleRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )


class LoggingMiddleware:
    """Middleware for request/response logging."""
    
    def __init__(self, app):
        self.app = app
        self.logger = structlog.get_logger("app.middleware")
    
    async def __call__(self, scope, receive, send):
        if scope["type"] != "http":
            await self.app(scope, receive, send)
            return
        
        request_id = self._generate_request_id()
        
        # Log request
        self.logger.info(
            "Request started",
            request_id=request_id,
            method=scope["method"],
            path=scope["path"],
            query_string=scope["query_string"].decode(),
            client=scope.get("client"),
            headers=dict(scope.get("headers", []))
        )
        
        # Process request
        await self.app(scope, receive, send)
        
        # Response logging is handled in the response middleware
    
    def _generate_request_id(self) -> str:
        """Generate unique request ID."""
        import uuid
        return str(uuid.uuid4())


def get_logger(name: str = None) -> structlog.BoundLogger:
    """Get a configured logger instance."""
    return structlog.get_logger(name or "app")


def log_user_action(
    user_id: int,
    action: str,
    resource: str = None,
    resource_id: int = None,
    metadata: Dict[str, Any] = None
) -> None:
    """
    Log user actions for audit trail.
    
    Args:
        user_id: ID of the user performing the action
        action: Action being performed (create, update, delete, etc.)
        resource: Resource being acted upon (user, promotion, etc.)
        resource_id: ID of the resource
        metadata: Additional metadata
    """
    logger = get_logger("app.audit")
    
    logger.info(
        "User action",
        user_id=user_id,
        action=action,
        resource=resource,
        resource_id=resource_id,
        metadata=metadata or {}
    )


def log_security_event(
    event_type: str,
    user_id: int = None,
    ip_address: str = None,
    user_agent: str = None,
    details: Dict[str, Any] = None
) -> None:
    """
    Log security-related events.
    
    Args:
        event_type: Type of security event
        user_id: User ID if applicable
        ip_address: Client IP address
        user_agent: Client user agent
        details: Additional event details
    """
    logger = get_logger("app.security")
    
    logger.warning(
        "Security event",
        event_type=event_type,
        user_id=user_id,
        ip_address=ip_address,
        user_agent=user_agent,
        details=details or {}
    )


def log_performance_metric(
    metric_name: str,
    value: float,
    unit: str = "ms",
    tags: Dict[str, str] = None
) -> None:
    """
    Log performance metrics.
    
    Args:
        metric_name: Name of the metric
        value: Metric value
        unit: Unit of measurement
        tags: Additional tags for the metric
    """
    logger = get_logger("app.performance")
    
    logger.info(
        "Performance metric",
        metric_name=metric_name,
        value=value,
        unit=unit,
        tags=tags or {}
    )


def log_business_event(
    event_type: str,
    entity_type: str,
    entity_id: int,
    user_id: int = None,
    metadata: Dict[str, Any] = None
) -> None:
    """
    Log business events for analytics.
    
    Args:
        event_type: Type of business event
        entity_type: Type of entity (promotion, merchant, etc.)
        entity_id: ID of the entity
        user_id: User ID if applicable
        metadata: Additional event metadata
    """
    logger = get_logger("app.business")
    
    logger.info(
        "Business event",
        event_type=event_type,
        entity_type=entity_type,
        entity_id=entity_id,
        user_id=user_id,
        metadata=metadata or {}
    )
