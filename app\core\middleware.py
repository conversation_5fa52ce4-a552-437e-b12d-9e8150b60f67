"""
Middleware for security, rate limiting, and request processing.
"""
import time
import json
from typing import Callable
from fastapi import Request, Response, HTTPException, status
from fastapi.middleware.base import BaseHTTPMiddleware
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded
import redis
import structlog

from app.core.config import settings

# Setup structured logging
logger = structlog.get_logger()

# Redis client for rate limiting
redis_client = redis.from_url(settings.REDIS_URL)

# Rate limiter
limiter = Limiter(
    key_func=get_remote_address,
    storage_uri=settings.REDIS_URL,
    default_limits=[f"{settings.RATE_LIMIT_PER_MINUTE}/minute"]
)


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """
    Middleware to add security headers to responses.
    """
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        response = await call_next(request)
        
        # Add security headers
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        response.headers["Permissions-Policy"] = "geolocation=(), microphone=(), camera=()"
        
        return response


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """
    Middleware to log requests and responses.
    """
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        start_time = time.time()
        
        # Log request
        logger.info(
            "Request started",
            method=request.method,
            url=str(request.url),
            client_ip=request.client.host if request.client else None,
            user_agent=request.headers.get("user-agent"),
        )
        
        # Process request
        response = await call_next(request)
        
        # Calculate processing time
        process_time = time.time() - start_time
        
        # Log response
        logger.info(
            "Request completed",
            method=request.method,
            url=str(request.url),
            status_code=response.status_code,
            process_time=round(process_time, 4),
        )
        
        # Add processing time header
        response.headers["X-Process-Time"] = str(process_time)
        
        return response


class IPWhitelistMiddleware(BaseHTTPMiddleware):
    """
    Middleware to whitelist IP addresses for admin endpoints.
    """
    
    def __init__(self, app, admin_whitelist: list = None):
        super().__init__(app)
        self.admin_whitelist = admin_whitelist or []
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Check if this is an admin endpoint
        if request.url.path.startswith("/api/v1/admin"):
            client_ip = request.client.host if request.client else None
            
            # If whitelist is configured and IP is not in whitelist
            if self.admin_whitelist and client_ip not in self.admin_whitelist:
                logger.warning(
                    "Unauthorized admin access attempt",
                    client_ip=client_ip,
                    path=request.url.path
                )
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Access denied from this IP address"
                )
        
        return await call_next(request)


class RequestSizeLimitMiddleware(BaseHTTPMiddleware):
    """
    Middleware to limit request body size.
    """
    
    def __init__(self, app, max_size: int = 10 * 1024 * 1024):  # 10MB default
        super().__init__(app)
        self.max_size = max_size
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Check content length
        content_length = request.headers.get("content-length")
        if content_length and int(content_length) > self.max_size:
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail=f"Request body too large. Maximum size: {self.max_size} bytes"
            )
        
        return await call_next(request)


class AnalyticsMiddleware(BaseHTTPMiddleware):
    """
    Middleware to collect analytics data.
    """
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        start_time = time.time()
        
        # Extract analytics data
        analytics_data = {
            "timestamp": start_time,
            "method": request.method,
            "path": request.url.path,
            "query_params": dict(request.query_params),
            "client_ip": request.client.host if request.client else None,
            "user_agent": request.headers.get("user-agent"),
            "referer": request.headers.get("referer"),
        }
        
        # Process request
        response = await call_next(request)
        
        # Add response data
        analytics_data.update({
            "status_code": response.status_code,
            "response_time": time.time() - start_time,
        })
        
        # Store analytics data (async task would be better for production)
        try:
            redis_client.lpush(
                "analytics_events",
                json.dumps(analytics_data)
            )
            # Keep only last 10000 events
            redis_client.ltrim("analytics_events", 0, 9999)
        except Exception as e:
            logger.error("Failed to store analytics data", error=str(e))
        
        return response


def setup_rate_limiting(app):
    """
    Setup rate limiting for the application.
    """
    app.state.limiter = limiter
    app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)
    
    return limiter
