"""
API documentation configuration.
"""
from typing import Dict, Any

from app.core.config import settings


def get_openapi_config() -> Dict[str, Any]:
    """Get OpenAPI configuration for the API documentation."""
    
    return {
        "title": settings.PROJECT_NAME,
        "version": settings.PROJECT_VERSION,
        "description": """
# Promodetect API

A comprehensive RESTful API for promotion detection and management.

## Features

- **User Management**: Registration, authentication, and profile management
- **Merchant Management**: Business profiles and location management
- **Promotion Management**: Create, manage, and discover promotions
- **Category System**: Hierarchical organization of promotions
- **Favorites**: Save and manage favorite promotions
- **Notifications**: Real-time user notifications
- **File Upload**: Image upload for avatars, logos, and promotion images
- **Multi-language Support**: English, French, and Arabic
- **Analytics**: Comprehensive tracking and statistics
- **Security**: JWT authentication, rate limiting, and security headers

## Authentication

This API uses JWT (JSON Web Tokens) for authentication. To access protected endpoints:

1. Register a new account using `/api/v1/auth/register`
2. <PERSON><PERSON> using `/api/v1/auth/login` to get access and refresh tokens
3. Include the access token in the Authorization header: `Bearer <token>`
4. Use the refresh token to get new access tokens when they expire

## Rate Limiting

The API implements rate limiting to ensure fair usage:
- Default: 60 requests per minute per IP address
- Rate limit headers are included in responses
- Exceeded limits return HTTP 429 status

## Error Handling

The API returns consistent error responses:
- HTTP status codes indicate the type of error
- Error responses include a `detail` field with error description
- Validation errors include detailed field-level error information

## Pagination

List endpoints support pagination with the following parameters:
- `page`: Page number (default: 1)
- `per_page`: Items per page (default: 20, max: 100)

Responses include pagination metadata:
- `total`: Total number of items
- `page`: Current page number
- `per_page`: Items per page
- `pages`: Total number of pages

## Filtering and Search

Many endpoints support filtering and search:
- Use query parameters to filter results
- Text search is available on relevant fields
- Date range filtering for time-based data
- Location-based filtering for geographical data

## File Uploads

The API supports file uploads for:
- User avatars
- Merchant logos and cover images
- Promotion images and galleries

Supported formats: JPEG, PNG, GIF, WebP
Maximum file size: 10MB per file

## Multi-language Support

The API supports multiple languages:
- English (en) - Default
- French (fr)
- Arabic (ar)

Content is automatically localized based on user preferences.
        """,
        "contact": {
            "name": "Promodetect API Support",
            "email": "<EMAIL>",
        },
        "license": {
            "name": "MIT License",
            "url": "https://opensource.org/licenses/MIT",
        },
        "servers": [
            {
                "url": "http://localhost:8000",
                "description": "Development server"
            },
            {
                "url": "https://api.promodetect.com",
                "description": "Production server"
            }
        ],
        "tags": [
            {
                "name": "Authentication",
                "description": "User authentication and authorization"
            },
            {
                "name": "Users",
                "description": "User management and profiles"
            },
            {
                "name": "Promotions",
                "description": "Promotion management and discovery"
            },
            {
                "name": "Merchants",
                "description": "Merchant profiles and business management"
            },
            {
                "name": "Categories",
                "description": "Promotion category management"
            },
            {
                "name": "Favorites",
                "description": "User favorites management"
            },
            {
                "name": "Notifications",
                "description": "User notification system"
            },
            {
                "name": "File Upload",
                "description": "File upload and media management"
            },
            {
                "name": "Analytics",
                "description": "Analytics and statistics"
            },
            {
                "name": "Admin",
                "description": "Administrative functions"
            }
        ],
        "components": {
            "securitySchemes": {
                "BearerAuth": {
                    "type": "http",
                    "scheme": "bearer",
                    "bearerFormat": "JWT",
                    "description": "JWT token obtained from login endpoint"
                }
            },
            "responses": {
                "UnauthorizedError": {
                    "description": "Authentication required",
                    "content": {
                        "application/json": {
                            "schema": {
                                "type": "object",
                                "properties": {
                                    "detail": {
                                        "type": "string",
                                        "example": "Not authenticated"
                                    }
                                }
                            }
                        }
                    }
                },
                "ForbiddenError": {
                    "description": "Insufficient permissions",
                    "content": {
                        "application/json": {
                            "schema": {
                                "type": "object",
                                "properties": {
                                    "detail": {
                                        "type": "string",
                                        "example": "Not enough permissions"
                                    }
                                }
                            }
                        }
                    }
                },
                "NotFoundError": {
                    "description": "Resource not found",
                    "content": {
                        "application/json": {
                            "schema": {
                                "type": "object",
                                "properties": {
                                    "detail": {
                                        "type": "string",
                                        "example": "Resource not found"
                                    }
                                }
                            }
                        }
                    }
                },
                "ValidationError": {
                    "description": "Validation error",
                    "content": {
                        "application/json": {
                            "schema": {
                                "type": "object",
                                "properties": {
                                    "detail": {
                                        "type": "string",
                                        "example": "Validation error"
                                    },
                                    "errors": {
                                        "type": "array",
                                        "items": {
                                            "type": "object",
                                            "properties": {
                                                "loc": {
                                                    "type": "array",
                                                    "items": {"type": "string"}
                                                },
                                                "msg": {"type": "string"},
                                                "type": {"type": "string"}
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
                "RateLimitError": {
                    "description": "Rate limit exceeded",
                    "content": {
                        "application/json": {
                            "schema": {
                                "type": "object",
                                "properties": {
                                    "detail": {
                                        "type": "string",
                                        "example": "Rate limit exceeded"
                                    }
                                }
                            }
                        }
                    }
                }
            }
        },
        "security": [
            {
                "BearerAuth": []
            }
        ]
    }


def get_redoc_config() -> Dict[str, Any]:
    """Get ReDoc configuration."""
    
    return {
        "title": f"{settings.PROJECT_NAME} - API Documentation",
        "redoc_js_url": "https://cdn.jsdelivr.net/npm/redoc@2.0.0/bundles/redoc.standalone.js",
        "redoc_favicon_url": "https://fastapi.tiangolo.com/img/favicon.png",
        "with_google_fonts": True
    }


def get_swagger_config() -> Dict[str, Any]:
    """Get Swagger UI configuration."""
    
    return {
        "title": f"{settings.PROJECT_NAME} - API Documentation",
        "swagger_js_url": "https://cdn.jsdelivr.net/npm/swagger-ui-dist@4.15.5/swagger-ui-bundle.js",
        "swagger_css_url": "https://cdn.jsdelivr.net/npm/swagger-ui-dist@4.15.5/swagger-ui.css",
        "swagger_favicon_url": "https://fastapi.tiangolo.com/img/favicon.png"
    }
