"""
Favorite service for managing user favorites.
"""
from typing import List, Tuple, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_
from fastapi import HTTPException, status

from app.models.user import UserFavorite, User
from app.models.promotion import Promotion
from app.services.base_service import BaseService


class FavoriteService(BaseService[UserFavorite]):
    """Service for managing user favorites."""
    
    def __init__(self, db: Session):
        super().__init__(UserFavorite, db)
    
    def add_favorite(self, user_id: int, promotion_id: int) -> UserFavorite:
        """
        Add a promotion to user's favorites.
        
        Args:
            user_id: User ID
            promotion_id: Promotion ID
            
        Returns:
            Created favorite
            
        Raises:
            HTTPException: If promotion not found or already favorited
        """
        # Check if promotion exists
        promotion = self.db.query(Promotion).filter(Promotion.id == promotion_id).first()
        if not promotion:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Promotion not found"
            )
        
        # Check if already favorited
        existing_favorite = self.db.query(UserFavorite).filter(
            and_(
                UserFavorite.user_id == user_id,
                UserFavorite.promotion_id == promotion_id,
                UserFavorite.is_active == True
            )
        ).first()
        
        if existing_favorite:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Promotion already in favorites"
            )
        
        # Create favorite
        favorite = UserFavorite(
            user_id=user_id,
            promotion_id=promotion_id
        )
        
        self.db.add(favorite)
        
        # Update promotion favorite count
        promotion.favorite_count += 1
        
        self.db.commit()
        self.db.refresh(favorite)
        
        return favorite
    
    def remove_favorite(self, user_id: int, promotion_id: int) -> bool:
        """
        Remove a promotion from user's favorites.
        
        Args:
            user_id: User ID
            promotion_id: Promotion ID
            
        Returns:
            True if removed successfully
            
        Raises:
            HTTPException: If favorite not found
        """
        favorite = self.db.query(UserFavorite).filter(
            and_(
                UserFavorite.user_id == user_id,
                UserFavorite.promotion_id == promotion_id,
                UserFavorite.is_active == True
            )
        ).first()
        
        if not favorite:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Favorite not found"
            )
        
        # Soft delete favorite
        favorite.is_active = False
        
        # Update promotion favorite count
        promotion = self.db.query(Promotion).filter(Promotion.id == promotion_id).first()
        if promotion and promotion.favorite_count > 0:
            promotion.favorite_count -= 1
        
        self.db.commit()
        
        return True
    
    def get_user_favorites(
        self, 
        user_id: int, 
        skip: int = 0, 
        limit: int = 100
    ) -> Tuple[List[Promotion], int]:
        """
        Get user's favorite promotions.
        
        Args:
            user_id: User ID
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            Tuple of (promotions, total_count)
        """
        # Query favorites with promotion details
        query = self.db.query(Promotion).join(UserFavorite).filter(
            and_(
                UserFavorite.user_id == user_id,
                UserFavorite.is_active == True,
                Promotion.is_active == True
            )
        ).order_by(UserFavorite.created_at.desc())
        
        total = query.count()
        promotions = query.offset(skip).limit(limit).all()
        
        return promotions, total
    
    def is_favorited(self, user_id: int, promotion_id: int) -> bool:
        """
        Check if a promotion is favorited by user.
        
        Args:
            user_id: User ID
            promotion_id: Promotion ID
            
        Returns:
            True if favorited
        """
        favorite = self.db.query(UserFavorite).filter(
            and_(
                UserFavorite.user_id == user_id,
                UserFavorite.promotion_id == promotion_id,
                UserFavorite.is_active == True
            )
        ).first()
        
        return favorite is not None
    
    def get_favorite_count(self, user_id: int) -> int:
        """
        Get total number of user's favorites.
        
        Args:
            user_id: User ID
            
        Returns:
            Number of favorites
        """
        return self.db.query(UserFavorite).filter(
            and_(
                UserFavorite.user_id == user_id,
                UserFavorite.is_active == True
            )
        ).count()
    
    def get_popular_promotions(
        self, 
        limit: int = 10,
        category_id: Optional[int] = None
    ) -> List[Promotion]:
        """
        Get most favorited promotions.
        
        Args:
            limit: Maximum number of promotions to return
            category_id: Optional category filter
            
        Returns:
            List of popular promotions
        """
        query = self.db.query(Promotion).filter(
            Promotion.is_active == True
        )
        
        if category_id:
            query = query.filter(Promotion.category_id == category_id)
        
        promotions = query.order_by(
            Promotion.favorite_count.desc()
        ).limit(limit).all()
        
        return promotions
    
    def bulk_check_favorites(
        self, 
        user_id: int, 
        promotion_ids: List[int]
    ) -> dict[int, bool]:
        """
        Check favorite status for multiple promotions.
        
        Args:
            user_id: User ID
            promotion_ids: List of promotion IDs
            
        Returns:
            Dictionary mapping promotion_id to favorite status
        """
        favorites = self.db.query(UserFavorite.promotion_id).filter(
            and_(
                UserFavorite.user_id == user_id,
                UserFavorite.promotion_id.in_(promotion_ids),
                UserFavorite.is_active == True
            )
        ).all()
        
        favorited_ids = {fav.promotion_id for fav in favorites}
        
        return {
            promotion_id: promotion_id in favorited_ids
            for promotion_id in promotion_ids
        }
