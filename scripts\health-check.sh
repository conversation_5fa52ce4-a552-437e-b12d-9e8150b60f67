#!/bin/bash

# Promodetect API Health Check Script
# This script performs comprehensive health checks after deployment

set -e

# Configuration
BACKEND_SERVER="**************"
DB_SERVER="**************"
API_BASE_URL="http://$BACKEND_SERVER"
MAX_RETRIES=30
RETRY_INTERVAL=10

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
    exit 1
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Function to check HTTP endpoint
check_http_endpoint() {
    local url=$1
    local expected_status=${2:-200}
    local description=$3
    
    log "Checking $description: $url"
    
    local response=$(curl -s -w "%{http_code}" -o /tmp/response.json "$url" || echo "000")
    
    if [ "$response" = "$expected_status" ]; then
        success "$description is healthy (HTTP $response)"
        return 0
    else
        error "$description failed (HTTP $response)"
        return 1
    fi
}

# Function to check API endpoint with JSON response
check_api_endpoint() {
    local endpoint=$1
    local description=$2
    local expected_field=$3
    
    log "Checking $description: $API_BASE_URL$endpoint"
    
    local response=$(curl -s "$API_BASE_URL$endpoint" || echo '{}')
    
    if echo "$response" | jq -e ".$expected_field" >/dev/null 2>&1; then
        success "$description is working correctly"
        return 0
    else
        error "$description failed - missing expected field: $expected_field"
        echo "Response: $response"
        return 1
    fi
}

# Function to check database connectivity
check_database_connectivity() {
    log "Checking database connectivity..."
    
    # Check if we can connect to the database through the API
    local response=$(curl -s "$API_BASE_URL/health" || echo '{}')
    
    if echo "$response" | jq -e '.status' >/dev/null 2>&1; then
        local status=$(echo "$response" | jq -r '.status')
        if [ "$status" = "healthy" ]; then
            success "Database connectivity is healthy"
            return 0
        else
            error "Database connectivity failed - API health status: $status"
            return 1
        fi
    else
        error "Database connectivity check failed - unable to get health status"
        return 1
    fi
}

# Function to check container health
check_container_health() {
    log "Checking container health on $BACKEND_SERVER..."
    
    ssh $DEPLOY_USER@$BACKEND_SERVER << 'EOF'
        # Check if containers are running
        if ! docker ps | grep -q "promodetect-api-app"; then
            echo "ERROR: API container is not running"
            exit 1
        fi
        
        if ! docker ps | grep -q "promodetect-api-redis"; then
            echo "ERROR: Redis container is not running"
            exit 1
        fi
        
        if ! docker ps | grep -q "promodetect-api-nginx"; then
            echo "ERROR: Nginx container is not running"
            exit 1
        fi
        
        # Check container health status
        api_health=$(docker inspect --format='{{.State.Health.Status}}' promodetect-api-app 2>/dev/null || echo "unknown")
        redis_health=$(docker inspect --format='{{.State.Health.Status}}' promodetect-api-redis 2>/dev/null || echo "unknown")
        
        echo "API container health: $api_health"
        echo "Redis container health: $redis_health"
        
        if [ "$api_health" != "healthy" ] && [ "$api_health" != "unknown" ]; then
            echo "ERROR: API container is not healthy"
            exit 1
        fi
        
        if [ "$redis_health" != "healthy" ] && [ "$redis_health" != "unknown" ]; then
            echo "ERROR: Redis container is not healthy"
            exit 1
        fi
        
        echo "All containers are running and healthy"
EOF
    
    if [ $? -eq 0 ]; then
        success "All containers are healthy"
    else
        error "Container health check failed"
    fi
}

# Function to check API performance
check_api_performance() {
    log "Checking API performance..."
    
    local start_time=$(date +%s%N)
    local response=$(curl -s -w "%{time_total}" -o /dev/null "$API_BASE_URL/health")
    local end_time=$(date +%s%N)
    
    local response_time_ms=$(echo "scale=2; $response * 1000" | bc)
    
    log "API response time: ${response_time_ms}ms"
    
    # Check if response time is acceptable (less than 2 seconds)
    if (( $(echo "$response < 2.0" | bc -l) )); then
        success "API performance is acceptable (${response_time_ms}ms)"
    else
        warning "API response time is slow (${response_time_ms}ms)"
    fi
}

# Function to check authentication endpoints
check_authentication() {
    log "Checking authentication endpoints..."
    
    # Test user registration endpoint
    local reg_response=$(curl -s -w "%{http_code}" -o /tmp/reg_response.json \
        -H "Content-Type: application/json" \
        -d '{
            "email": "<EMAIL>",
            "username": "healthcheck",
            "password": "HealthCheck123!",
            "confirm_password": "HealthCheck123!",
            "first_name": "Health",
            "last_name": "Check"
        }' \
        "$API_BASE_URL/api/v1/auth/register" || echo "000")
    
    if [ "$reg_response" = "201" ] || [ "$reg_response" = "400" ]; then
        success "Registration endpoint is responding correctly"
    else
        warning "Registration endpoint returned unexpected status: $reg_response"
    fi
    
    # Test login endpoint with invalid credentials
    local login_response=$(curl -s -w "%{http_code}" -o /tmp/login_response.json \
        -H "Content-Type: application/json" \
        -d '{
            "email": "<EMAIL>",
            "password": "invalidpassword"
        }' \
        "$API_BASE_URL/api/v1/auth/login" || echo "000")
    
    if [ "$login_response" = "401" ]; then
        success "Login endpoint is responding correctly"
    else
        warning "Login endpoint returned unexpected status: $login_response"
    fi
}

# Function to check file upload capability
check_file_upload() {
    log "Checking file upload capability..."
    
    # Check if uploads directory is accessible
    local uploads_response=$(curl -s -w "%{http_code}" -o /dev/null "$API_BASE_URL/uploads/" || echo "000")
    
    if [ "$uploads_response" = "200" ] || [ "$uploads_response" = "403" ] || [ "$uploads_response" = "404" ]; then
        success "File upload infrastructure is accessible"
    else
        warning "File upload infrastructure may have issues (HTTP $uploads_response)"
    fi
}

# Function to check external dependencies
check_external_dependencies() {
    log "Checking external dependencies..."
    
    # Check Redis connectivity through the application
    ssh $DEPLOY_USER@$BACKEND_SERVER << 'EOF'
        # Test Redis connectivity
        if docker exec promodetect-api-redis redis-cli ping | grep -q "PONG"; then
            echo "Redis is responding correctly"
        else
            echo "ERROR: Redis is not responding"
            exit 1
        fi
EOF
    
    if [ $? -eq 0 ]; then
        success "Redis connectivity is healthy"
    else
        error "Redis connectivity check failed"
    fi
}

# Function to check logs for errors
check_logs_for_errors() {
    log "Checking application logs for errors..."
    
    ssh $DEPLOY_USER@$BACKEND_SERVER << 'EOF'
        # Check for recent errors in application logs
        if docker logs promodetect-api-app --since="5m" 2>&1 | grep -i "error\|exception\|traceback" | head -5; then
            echo "WARNING: Found recent errors in application logs"
        else
            echo "No recent errors found in application logs"
        fi
EOF
}

# Function to perform load test
perform_load_test() {
    log "Performing basic load test..."
    
    local concurrent_requests=10
    local total_requests=100
    
    # Simple load test using curl
    for i in $(seq 1 $concurrent_requests); do
        (
            for j in $(seq 1 $((total_requests / concurrent_requests))); do
                curl -s "$API_BASE_URL/health" >/dev/null
            done
        ) &
    done
    
    wait
    
    success "Basic load test completed"
}

# Function to send notification
send_notification() {
    local status=$1
    local message=$2
    
    if [ -n "$SLACK_WEBHOOK_URL" ]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"🚀 Promodetect API Deployment $status: $message\"}" \
            "$SLACK_WEBHOOK_URL" >/dev/null 2>&1 || true
    fi
    
    if [ -n "$DISCORD_WEBHOOK_URL" ]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"content\":\"🚀 Promodetect API Deployment $status: $message\"}" \
            "$DISCORD_WEBHOOK_URL" >/dev/null 2>&1 || true
    fi
}

# Main health check function
main() {
    log "Starting comprehensive health checks for Promodetect API..."
    log "Target server: $BACKEND_SERVER"
    log "Database server: $DB_SERVER"
    
    local failed_checks=0
    
    # Wait for application to be ready
    log "Waiting for application to be ready..."
    for i in $(seq 1 $MAX_RETRIES); do
        if curl -s "$API_BASE_URL/health" >/dev/null 2>&1; then
            log "Application is responding"
            break
        fi
        if [ $i -eq $MAX_RETRIES ]; then
            error "Application failed to respond after $MAX_RETRIES attempts"
        fi
        log "Attempt $i/$MAX_RETRIES - waiting ${RETRY_INTERVAL}s..."
        sleep $RETRY_INTERVAL
    done
    
    # Perform health checks
    log "Performing health checks..."
    
    # Basic HTTP checks
    check_http_endpoint "$API_BASE_URL/health" 200 "Health endpoint" || ((failed_checks++))
    check_http_endpoint "$API_BASE_URL/" 200 "Root endpoint" || ((failed_checks++))
    check_api_endpoint "/api/v1/info" "API info endpoint" "name" || ((failed_checks++))
    
    # Database connectivity
    check_database_connectivity || ((failed_checks++))
    
    # Container health
    check_container_health || ((failed_checks++))
    
    # API performance
    check_api_performance || true  # Don't fail on performance issues
    
    # Authentication endpoints
    check_authentication || ((failed_checks++))
    
    # File upload capability
    check_file_upload || true  # Don't fail on upload issues
    
    # External dependencies
    check_external_dependencies || ((failed_checks++))
    
    # Check logs for errors
    check_logs_for_errors || true  # Don't fail on log warnings
    
    # Perform basic load test
    perform_load_test || true  # Don't fail on load test issues
    
    # Summary
    log "Health check summary:"
    log "Failed checks: $failed_checks"
    
    if [ $failed_checks -eq 0 ]; then
        success "All critical health checks passed! 🎉"
        send_notification "SUCCESS" "All health checks passed. Application is healthy and ready."
        exit 0
    else
        error "$failed_checks health checks failed! ❌"
        send_notification "FAILED" "$failed_checks health checks failed. Please investigate."
        exit 1
    fi
}

# Run health checks
main "$@"
