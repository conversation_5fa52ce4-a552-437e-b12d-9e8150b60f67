"""
User service for business logic.
"""
from typing import Op<PERSON>, List, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from fastapi import HTTPException, status
from datetime import datetime, timedelta

from app.models.user import User, UserRole, UserStatus, UserSession
from app.schemas.user import UserCreate, UserUpdate, UserSearch
from app.core.security import get_password_hash, verify_password, generate_session_token
from app.services.base_service import BaseService


class UserService(BaseService[User]):
    """User service for managing user operations."""
    
    def __init__(self, db: Session):
        super().__init__(User, db)
    
    def create_user(self, user_data: UserCreate) -> User:
        """
        Create a new user.
        
        Args:
            user_data: User creation data
            
        Returns:
            Created user
            
        Raises:
            HTTPException: If email or username already exists
        """
        # Check if email already exists
        if self.get_by_email(user_data.email):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered"
            )
        
        # Check if username already exists
        if self.get_by_username(user_data.username):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Username already taken"
            )
        
        # Create user
        user = User(
            email=user_data.email,
            username=user_data.username,
            hashed_password=get_password_hash(user_data.password),
            first_name=user_data.first_name,
            last_name=user_data.last_name,
            phone_number=user_data.phone_number,
            country=user_data.country,
            city=user_data.city,
            preferred_language=user_data.preferred_language,
            role=UserRole.CUSTOMER,
            status=UserStatus.PENDING_VERIFICATION,
        )
        
        self.db.add(user)
        self.db.commit()
        self.db.refresh(user)
        
        return user
    
    def get_by_email(self, email: str) -> Optional[User]:
        """Get user by email."""
        return self.db.query(User).filter(User.email == email).first()
    
    def get_by_username(self, username: str) -> Optional[User]:
        """Get user by username."""
        return self.db.query(User).filter(User.username == username).first()
    
    def authenticate_user(self, email: str, password: str) -> Optional[User]:
        """
        Authenticate user with email and password.
        
        Args:
            email: User email
            password: User password
            
        Returns:
            User if authentication successful, None otherwise
        """
        user = self.get_by_email(email)
        if not user or not verify_password(password, user.hashed_password):
            return None
        
        # Update last login
        user.last_login = datetime.utcnow()
        self.db.commit()
        
        return user
    
    def update_user(self, user_id: int, user_data: UserUpdate) -> User:
        """
        Update user information.
        
        Args:
            user_id: User ID
            user_data: User update data
            
        Returns:
            Updated user
            
        Raises:
            HTTPException: If user not found
        """
        user = self.get_by_id(user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Update fields
        for field, value in user_data.dict(exclude_unset=True).items():
            setattr(user, field, value)
        
        user.updated_at = datetime.utcnow()
        self.db.commit()
        self.db.refresh(user)
        
        return user
    
    def change_password(self, user_id: int, current_password: str, new_password: str) -> bool:
        """
        Change user password.
        
        Args:
            user_id: User ID
            current_password: Current password
            new_password: New password
            
        Returns:
            True if password changed successfully
            
        Raises:
            HTTPException: If user not found or current password is incorrect
        """
        user = self.get_by_id(user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        if not verify_password(current_password, user.hashed_password):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Current password is incorrect"
            )
        
        user.hashed_password = get_password_hash(new_password)
        user.updated_at = datetime.utcnow()
        self.db.commit()
        
        return True
    
    def verify_email(self, user_id: int) -> User:
        """
        Verify user email.
        
        Args:
            user_id: User ID
            
        Returns:
            Updated user
            
        Raises:
            HTTPException: If user not found
        """
        user = self.get_by_id(user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        user.email_verified = True
        user.email_verified_at = datetime.utcnow()
        user.is_verified = True
        user.status = UserStatus.ACTIVE
        user.updated_at = datetime.utcnow()
        
        self.db.commit()
        self.db.refresh(user)
        
        return user
    
    def search_users(self, search_params: UserSearch) -> Tuple[List[User], int]:
        """
        Search users with filters and pagination.
        
        Args:
            search_params: Search parameters
            
        Returns:
            Tuple of (users, total_count)
        """
        query = self.db.query(User)
        
        # Apply filters
        if search_params.query:
            query = query.filter(
                or_(
                    User.username.ilike(f"%{search_params.query}%"),
                    User.first_name.ilike(f"%{search_params.query}%"),
                    User.last_name.ilike(f"%{search_params.query}%"),
                    User.email.ilike(f"%{search_params.query}%")
                )
            )
        
        if search_params.role:
            query = query.filter(User.role == search_params.role)
        
        if search_params.status:
            query = query.filter(User.status == search_params.status)
        
        if search_params.country:
            query = query.filter(User.country == search_params.country)
        
        if search_params.city:
            query = query.filter(User.city == search_params.city)
        
        if search_params.is_verified is not None:
            query = query.filter(User.is_verified == search_params.is_verified)
        
        # Get total count
        total = query.count()
        
        # Apply pagination
        offset = (search_params.page - 1) * search_params.per_page
        users = query.offset(offset).limit(search_params.per_page).all()
        
        return users, total
    
    def create_session(self, user_id: int, ip_address: str = None, user_agent: str = None) -> UserSession:
        """
        Create a new user session.
        
        Args:
            user_id: User ID
            ip_address: Client IP address
            user_agent: Client user agent
            
        Returns:
            Created session
        """
        session = UserSession(
            user_id=user_id,
            session_token=generate_session_token(),
            ip_address=ip_address,
            user_agent=user_agent,
            expires_at=datetime.utcnow() + timedelta(days=7)  # 7 days
        )
        
        self.db.add(session)
        self.db.commit()
        self.db.refresh(session)
        
        return session
