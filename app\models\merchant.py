"""
Merchant model and related entities.
"""
from sqlalchemy import Column, String, Text, Float, Boolean, ForeignKey, Integer, Enum
from sqlalchemy.orm import relationship
import enum

from app.models.base import BaseModel


class MerchantStatus(str, enum.Enum):
    """Merchant status enumeration."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    PENDING_APPROVAL = "pending_approval"
    SUSPENDED = "suspended"
    REJECTED = "rejected"


class BusinessType(str, enum.Enum):
    """Business type enumeration."""
    RESTAURANT = "restaurant"
    RETAIL = "retail"
    GROCERY = "grocery"
    ELECTRONICS = "electronics"
    FASHION = "fashion"
    BEAUTY = "beauty"
    HEALTH = "health"
    AUTOMOTIVE = "automotive"
    HOME_GARDEN = "home_garden"
    SPORTS = "sports"
    ENTERTAINMENT = "entertainment"
    SERVICES = "services"
    OTHER = "other"


class Merchant(BaseModel):
    """Merchant model."""
    __tablename__ = "merchants"
    
    # User Association
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, unique=True)
    
    # Business Information
    business_name = Column(String(200), nullable=False, index=True)
    business_type = Column(Enum(BusinessType), nullable=False)
    business_description = Column(Text, nullable=True)
    business_registration_number = Column(String(100), nullable=True)
    tax_id = Column(String(100), nullable=True)
    
    # Contact Information
    business_email = Column(String(255), nullable=True)
    business_phone = Column(String(20), nullable=True)
    website_url = Column(String(500), nullable=True)
    
    # Location Information
    business_address = Column(Text, nullable=False)
    city = Column(String(100), nullable=False)
    state_province = Column(String(100), nullable=True)
    country = Column(String(100), nullable=False)
    postal_code = Column(String(20), nullable=True)
    latitude = Column(Float, nullable=True)
    longitude = Column(Float, nullable=True)
    
    # Business Details
    logo_url = Column(String(500), nullable=True)
    cover_image_url = Column(String(500), nullable=True)
    operating_hours = Column(Text, nullable=True)  # JSON string
    social_media_links = Column(Text, nullable=True)  # JSON string
    
    # Status and Verification
    status = Column(Enum(MerchantStatus), default=MerchantStatus.PENDING_APPROVAL, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)
    verification_documents = Column(Text, nullable=True)  # JSON string
    
    # Ratings and Reviews
    average_rating = Column(Float, default=0.0, nullable=False)
    total_reviews = Column(Integer, default=0, nullable=False)
    
    # Business Metrics
    total_promotions = Column(Integer, default=0, nullable=False)
    active_promotions = Column(Integer, default=0, nullable=False)
    
    # Relationships
    user = relationship("User", back_populates="merchant_profile")
    promotions = relationship("Promotion", back_populates="merchant")
    locations = relationship("MerchantLocation", back_populates="merchant")
    reviews = relationship("Review", back_populates="merchant")


class MerchantLocation(BaseModel):
    """Additional merchant locations."""
    __tablename__ = "merchant_locations"
    
    merchant_id = Column(Integer, ForeignKey("merchants.id"), nullable=False)
    
    # Location Information
    location_name = Column(String(200), nullable=False)
    address = Column(Text, nullable=False)
    city = Column(String(100), nullable=False)
    state_province = Column(String(100), nullable=True)
    country = Column(String(100), nullable=False)
    postal_code = Column(String(20), nullable=True)
    latitude = Column(Float, nullable=True)
    longitude = Column(Float, nullable=True)
    
    # Contact Information
    phone = Column(String(20), nullable=True)
    email = Column(String(255), nullable=True)
    
    # Operating Information
    operating_hours = Column(Text, nullable=True)  # JSON string
    is_main_location = Column(Boolean, default=False, nullable=False)
    
    # Relationships
    merchant = relationship("Merchant", back_populates="locations")
    promotions = relationship("Promotion", back_populates="location")
