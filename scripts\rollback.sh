#!/bin/bash

# Promodetect API Rollback Script
# This script rolls back to the previous version in case of deployment failure

set -e

# Configuration
BACKEND_SERVER="**************"
DEPLOY_USER="${DEPLOY_USER:-deploy}"
APP_NAME="promodetect-api"
DEPLOY_PATH="/opt/promodetect"
CONTAINER_NAME="${APP_NAME}-app"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
    exit 1
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Function to get available backup images
get_backup_images() {
    log "Getting available backup images..."
    
    ssh $DEPLOY_USER@$BACKEND_SERVER << 'EOF'
        echo "Available backup images:"
        docker images --filter "reference=promodetect-api:backup-*" --format "table {{.Repository}}:{{.Tag}}\t{{.CreatedAt}}\t{{.Size}}"
EOF
}

# Function to get the latest backup image
get_latest_backup() {
    ssh $DEPLOY_USER@$BACKEND_SERVER << 'EOF'
        # Get the most recent backup image
        LATEST_BACKUP=$(docker images --filter "reference=promodetect-api:backup-*" --format "{{.Repository}}:{{.Tag}}" | head -1)
        
        if [ -z "$LATEST_BACKUP" ]; then
            echo "ERROR: No backup images found"
            exit 1
        fi
        
        echo "$LATEST_BACKUP"
EOF
}

# Function to verify backup image
verify_backup_image() {
    local backup_image=$1
    
    log "Verifying backup image: $backup_image"
    
    ssh $DEPLOY_USER@$BACKEND_SERVER << EOF
        # Check if the backup image exists
        if ! docker images | grep -q "$backup_image"; then
            echo "ERROR: Backup image $backup_image not found"
            exit 1
        fi
        
        # Test the backup image
        echo "Testing backup image..."
        docker run --rm $backup_image python -c "import app; print('Image verification successful')" || {
            echo "ERROR: Backup image verification failed"
            exit 1
        }
        
        echo "Backup image verification successful"
EOF
    
    if [ $? -eq 0 ]; then
        success "Backup image verified successfully"
    else
        error "Backup image verification failed"
    fi
}

# Function to create pre-rollback backup
create_pre_rollback_backup() {
    log "Creating pre-rollback backup of current state..."
    
    ssh $DEPLOY_USER@$BACKEND_SERVER << 'EOF'
        if docker ps -q -f name=promodetect-api-app; then
            # Create backup of current state before rollback
            CURRENT_IMAGE=$(docker inspect promodetect-api-app --format='{{.Config.Image}}')
            docker tag $CURRENT_IMAGE promodetect-api:pre-rollback-$(date +%Y%m%d-%H%M%S)
            echo "Pre-rollback backup created: promodetect-api:pre-rollback-$(date +%Y%m%d-%H%M%S)"
        fi
EOF
    
    success "Pre-rollback backup created"
}

# Function to stop current application
stop_current_application() {
    log "Stopping current application..."
    
    ssh $DEPLOY_USER@$BACKEND_SERVER << EOF
        cd $DEPLOY_PATH
        
        # Gracefully stop the current application
        if docker ps -q -f name=promodetect-api-app; then
            echo "Stopping current application container..."
            docker stop promodetect-api-app || true
            docker rm promodetect-api-app || true
        fi
EOF
    
    success "Current application stopped"
}

# Function to rollback to backup image
rollback_to_backup() {
    local backup_image=$1
    
    log "Rolling back to backup image: $backup_image"
    
    ssh $DEPLOY_USER@$BACKEND_SERVER << EOF
        cd $DEPLOY_PATH
        
        # Update docker-compose to use backup image
        export CI_REGISTRY_IMAGE=\$(echo "$backup_image" | cut -d':' -f1)
        export CI_COMMIT_SHA=\$(echo "$backup_image" | cut -d':' -f2 | sed 's/backup-//')
        
        # Start the backup image
        docker run -d \
            --name promodetect-api-app \
            --env-file ./config/.env \
            --volume ./uploads:/app/uploads \
            --volume ./logs:/app/logs \
            --network promodetect_promodetect-network \
            --restart unless-stopped \
            $backup_image
        
        # Wait for the application to start
        echo "Waiting for application to start..."
        for i in {1..30}; do
            if docker exec promodetect-api-app curl -f http://localhost:8000/health >/dev/null 2>&1; then
                echo "Application started successfully"
                break
            fi
            if [ \$i -eq 30 ]; then
                echo "ERROR: Application failed to start after rollback"
                exit 1
            fi
            sleep 2
        done
EOF
    
    if [ $? -eq 0 ]; then
        success "Rollback completed successfully"
    else
        error "Rollback failed"
    fi
}

# Function to verify rollback
verify_rollback() {
    log "Verifying rollback..."
    
    # Check if application is responding
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "http://$BACKEND_SERVER/health" >/dev/null 2>&1; then
            success "Application is responding after rollback"
            break
        fi
        
        if [ $attempt -eq $max_attempts ]; then
            error "Application is not responding after rollback"
        fi
        
        log "Attempt $attempt/$max_attempts - waiting for application..."
        sleep 5
        ((attempt++))
    done
    
    # Perform basic health checks
    log "Performing basic health checks..."
    
    local health_response=$(curl -s "http://$BACKEND_SERVER/health" || echo '{}')
    
    if echo "$health_response" | grep -q '"status":"healthy"'; then
        success "Health check passed after rollback"
    else
        warning "Health check failed after rollback"
        echo "Health response: $health_response"
    fi
    
    # Check API info endpoint
    local info_response=$(curl -s "http://$BACKEND_SERVER/api/v1/info" || echo '{}')
    
    if echo "$info_response" | grep -q '"name"'; then
        success "API info endpoint is working after rollback"
    else
        warning "API info endpoint failed after rollback"
    fi
}

# Function to update nginx configuration if needed
update_nginx_after_rollback() {
    log "Updating Nginx configuration after rollback..."
    
    ssh $DEPLOY_USER@$BACKEND_SERVER << EOF
        cd $DEPLOY_PATH
        
        # Restart nginx to ensure it's pointing to the rolled back application
        if docker ps -q -f name=promodetect-api-nginx; then
            docker restart promodetect-api-nginx
            echo "Nginx restarted"
        fi
EOF
    
    success "Nginx configuration updated"
}

# Function to clean up failed deployment artifacts
cleanup_failed_deployment() {
    log "Cleaning up failed deployment artifacts..."
    
    ssh $DEPLOY_USER@$BACKEND_SERVER << 'EOF'
        # Remove any dangling containers
        docker container prune -f
        
        # Remove any dangling images (but keep backups)
        docker image prune -f --filter "label!=backup"
        
        echo "Cleanup completed"
EOF
    
    success "Failed deployment artifacts cleaned up"
}

# Function to send rollback notification
send_rollback_notification() {
    local status=$1
    local backup_image=$2
    
    local message="Rollback to $backup_image $status"
    
    if [ -n "$SLACK_WEBHOOK_URL" ]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"🔄 Promodetect API Rollback $status: $message\"}" \
            "$SLACK_WEBHOOK_URL" >/dev/null 2>&1 || true
    fi
    
    if [ -n "$DISCORD_WEBHOOK_URL" ]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"content\":\"🔄 Promodetect API Rollback $status: $message\"}" \
            "$DISCORD_WEBHOOK_URL" >/dev/null 2>&1 || true
    fi
}

# Function to show rollback instructions
show_rollback_instructions() {
    log "Rollback Instructions:"
    echo "1. This script will rollback to the most recent backup image"
    echo "2. The current state will be backed up before rollback"
    echo "3. The application will be stopped and restarted with the backup image"
    echo "4. Health checks will be performed to verify the rollback"
    echo ""
    echo "Available backup images:"
    get_backup_images
    echo ""
    read -p "Do you want to proceed with the rollback? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log "Rollback cancelled by user"
        exit 0
    fi
}

# Main rollback function
main() {
    log "Starting rollback process for Promodetect API..."
    log "Target server: $BACKEND_SERVER"
    
    # Show rollback instructions and get confirmation
    if [ "${AUTO_ROLLBACK:-false}" != "true" ]; then
        show_rollback_instructions
    fi
    
    # Get the latest backup image
    local backup_image
    backup_image=$(get_latest_backup)
    
    if [ -z "$backup_image" ]; then
        error "No backup image found for rollback"
    fi
    
    log "Rolling back to: $backup_image"
    
    # Verify backup image
    verify_backup_image "$backup_image"
    
    # Create pre-rollback backup
    create_pre_rollback_backup
    
    # Stop current application
    stop_current_application
    
    # Rollback to backup image
    rollback_to_backup "$backup_image"
    
    # Update nginx configuration
    update_nginx_after_rollback
    
    # Verify rollback
    verify_rollback
    
    # Clean up failed deployment artifacts
    cleanup_failed_deployment
    
    # Send notification
    send_rollback_notification "COMPLETED" "$backup_image"
    
    success "Rollback completed successfully! 🔄"
    log "Application has been rolled back to: $backup_image"
    log "Application is now running at: http://$BACKEND_SERVER"
    log "API Documentation: http://$BACKEND_SERVER/api/v1/docs"
}

# Error handling
trap 'error "Rollback failed at line $LINENO"' ERR

# Run rollback
main "$@"
