"""
Notification models.
"""
from sqlalchemy import Column, <PERSON>, Text, Integer, Boolean, Foreign<PERSON>ey, Enum, DateTime
from sqlalchemy.orm import relationship
import enum

from app.models.base import BaseModel


class NotificationType(str, enum.Enum):
    """Notification type enumeration."""
    PROMOTION_ALERT = "promotion_alert"
    MERCHANT_UPDATE = "merchant_update"
    REVIEW_RESPONSE = "review_response"
    SYSTEM_ANNOUNCEMENT = "system_announcement"
    ACCOUNT_UPDATE = "account_update"
    SECURITY_ALERT = "security_alert"
    MARKETING = "marketing"
    REMINDER = "reminder"


class NotificationChannel(str, enum.Enum):
    """Notification delivery channel enumeration."""
    IN_APP = "in_app"
    EMAIL = "email"
    SMS = "sms"
    PUSH = "push"


class NotificationPriority(str, enum.Enum):
    """Notification priority enumeration."""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"


class Notification(BaseModel):
    """Notification model."""
    __tablename__ = "notifications"
    
    # Target User
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Notification Content
    title = Column(String(200), nullable=False)
    message = Column(Text, nullable=False)
    notification_type = Column(Enum(NotificationType), nullable=False)
    priority = Column(Enum(NotificationPriority), default=NotificationPriority.NORMAL, nullable=False)
    
    # Delivery
    channels = Column(String(100), nullable=False)  # Comma-separated channels
    is_read = Column(Boolean, default=False, nullable=False)
    read_at = Column(DateTime, nullable=True)
    
    # Related Entities
    related_promotion_id = Column(Integer, ForeignKey("promotions.id"), nullable=True)
    related_merchant_id = Column(Integer, ForeignKey("merchants.id"), nullable=True)
    
    # Action
    action_url = Column(String(500), nullable=True)
    action_text = Column(String(100), nullable=True)
    
    # Metadata
    metadata = Column(Text, nullable=True)  # JSON string for additional data
    
    # Delivery Status
    email_sent = Column(Boolean, default=False, nullable=False)
    sms_sent = Column(Boolean, default=False, nullable=False)
    push_sent = Column(Boolean, default=False, nullable=False)
    
    # Relationships
    user = relationship("User", back_populates="notifications")
    related_promotion = relationship("Promotion")
    related_merchant = relationship("Merchant")


class NotificationTemplate(BaseModel):
    """Notification template for consistent messaging."""
    __tablename__ = "notification_templates"
    
    # Template Information
    name = Column(String(100), unique=True, nullable=False)
    notification_type = Column(Enum(NotificationType), nullable=False)
    
    # Template Content
    title_template = Column(String(200), nullable=False)
    message_template = Column(Text, nullable=False)
    
    # Multilingual Templates
    title_template_en = Column(String(200), nullable=True)
    title_template_fr = Column(String(200), nullable=True)
    title_template_ar = Column(String(200), nullable=True)
    message_template_en = Column(Text, nullable=True)
    message_template_fr = Column(Text, nullable=True)
    message_template_ar = Column(Text, nullable=True)
    
    # Default Settings
    default_channels = Column(String(100), nullable=False)
    default_priority = Column(Enum(NotificationPriority), default=NotificationPriority.NORMAL, nullable=False)
    
    def get_title_template(self, language: str = "en") -> str:
        """Get title template in specified language."""
        if language == "fr" and self.title_template_fr:
            return self.title_template_fr
        elif language == "ar" and self.title_template_ar:
            return self.title_template_ar
        elif language == "en" and self.title_template_en:
            return self.title_template_en
        return self.title_template
    
    def get_message_template(self, language: str = "en") -> str:
        """Get message template in specified language."""
        if language == "fr" and self.message_template_fr:
            return self.message_template_fr
        elif language == "ar" and self.message_template_ar:
            return self.message_template_ar
        elif language == "en" and self.message_template_en:
            return self.message_template_en
        return self.message_template
