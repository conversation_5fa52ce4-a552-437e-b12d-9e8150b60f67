"""
Promotion model and related entities.
"""
from datetime import datetime
from sqlalchemy import Column, String, Text, Float, Integer, Boolean, DateTime, ForeignKey, Enum
from sqlalchemy.orm import relationship
import enum

from app.models.base import BaseModel


class PromotionType(str, enum.Enum):
    """Promotion type enumeration."""
    DISCOUNT = "discount"
    BOGO = "bogo"  # Buy One Get One
    FREE_SHIPPING = "free_shipping"
    CASHBACK = "cashback"
    COUPON = "coupon"
    FLASH_SALE = "flash_sale"
    SEASONAL = "seasonal"
    LOYALTY = "loyalty"
    REFERRAL = "referral"
    OTHER = "other"


class PromotionStatus(str, enum.Enum):
    """Promotion status enumeration."""
    DRAFT = "draft"
    ACTIVE = "active"
    PAUSED = "paused"
    EXPIRED = "expired"
    CANCELLED = "cancelled"
    PENDING_APPROVAL = "pending_approval"
    REJECTED = "rejected"


class DiscountType(str, enum.Enum):
    """Discount type enumeration."""
    PERCENTAGE = "percentage"
    FIXED_AMOUNT = "fixed_amount"
    FREE_ITEM = "free_item"


class Promotion(BaseModel):
    """Promotion model."""
    __tablename__ = "promotions"
    
    # Basic Information
    title = Column(String(200), nullable=False, index=True)
    description = Column(Text, nullable=False)
    short_description = Column(String(500), nullable=True)
    
    # Merchant and Category
    merchant_id = Column(Integer, ForeignKey("merchants.id"), nullable=False)
    category_id = Column(Integer, ForeignKey("categories.id"), nullable=False)
    location_id = Column(Integer, ForeignKey("merchant_locations.id"), nullable=True)
    
    # Promotion Details
    promotion_type = Column(Enum(PromotionType), nullable=False)
    discount_type = Column(Enum(DiscountType), nullable=True)
    discount_value = Column(Float, nullable=True)  # Percentage or fixed amount
    original_price = Column(Float, nullable=True)
    discounted_price = Column(Float, nullable=True)
    
    # Validity
    start_date = Column(DateTime, nullable=False)
    end_date = Column(DateTime, nullable=False)
    is_unlimited = Column(Boolean, default=False, nullable=False)
    max_uses = Column(Integer, nullable=True)
    max_uses_per_user = Column(Integer, nullable=True)
    current_uses = Column(Integer, default=0, nullable=False)
    
    # Status and Visibility
    status = Column(Enum(PromotionStatus), default=PromotionStatus.DRAFT, nullable=False)
    is_featured = Column(Boolean, default=False, nullable=False)
    is_exclusive = Column(Boolean, default=False, nullable=False)
    requires_membership = Column(Boolean, default=False, nullable=False)
    
    # Media
    image_url = Column(String(500), nullable=True)
    gallery_urls = Column(Text, nullable=True)  # JSON array of image URLs
    video_url = Column(String(500), nullable=True)
    
    # Terms and Conditions
    terms_conditions = Column(Text, nullable=True)
    minimum_purchase = Column(Float, nullable=True)
    applicable_products = Column(Text, nullable=True)  # JSON array
    excluded_products = Column(Text, nullable=True)  # JSON array
    
    # Coupon Information
    coupon_code = Column(String(50), nullable=True, index=True)
    auto_apply = Column(Boolean, default=False, nullable=False)
    
    # Multilingual Support
    title_en = Column(String(200), nullable=True)
    title_fr = Column(String(200), nullable=True)
    title_ar = Column(String(200), nullable=True)
    description_en = Column(Text, nullable=True)
    description_fr = Column(Text, nullable=True)
    description_ar = Column(Text, nullable=True)
    
    # Analytics
    view_count = Column(Integer, default=0, nullable=False)
    click_count = Column(Integer, default=0, nullable=False)
    conversion_count = Column(Integer, default=0, nullable=False)
    favorite_count = Column(Integer, default=0, nullable=False)
    
    # SEO
    meta_title = Column(String(200), nullable=True)
    meta_description = Column(String(500), nullable=True)
    slug = Column(String(200), unique=True, nullable=True, index=True)
    
    # External Source Information (for scraped promotions)
    source_url = Column(String(1000), nullable=True)
    source_name = Column(String(100), nullable=True)
    external_id = Column(String(100), nullable=True)
    last_scraped_at = Column(DateTime, nullable=True)
    
    # Relationships
    merchant = relationship("Merchant", back_populates="promotions")
    category = relationship("Category", back_populates="promotions")
    location = relationship("MerchantLocation", back_populates="promotions")
    favorites = relationship("UserFavorite", back_populates="promotion")
    reviews = relationship("Review", back_populates="promotion")
    analytics_events = relationship("AnalyticsEvent", back_populates="promotion")
    
    def get_title(self, language: str = "en") -> str:
        """Get promotion title in specified language."""
        if language == "fr" and self.title_fr:
            return self.title_fr
        elif language == "ar" and self.title_ar:
            return self.title_ar
        elif language == "en" and self.title_en:
            return self.title_en
        return self.title
    
    def get_description(self, language: str = "en") -> str:
        """Get promotion description in specified language."""
        if language == "fr" and self.description_fr:
            return self.description_fr
        elif language == "ar" and self.description_ar:
            return self.description_ar
        elif language == "en" and self.description_en:
            return self.description_en
        return self.description
    
    @property
    def is_active(self) -> bool:
        """Check if promotion is currently active."""
        now = datetime.utcnow()
        return (
            self.status == PromotionStatus.ACTIVE and
            self.start_date <= now <= self.end_date and
            (self.is_unlimited or self.current_uses < (self.max_uses or float('inf')))
        )
    
    @property
    def is_expired(self) -> bool:
        """Check if promotion is expired."""
        return datetime.utcnow() > self.end_date
