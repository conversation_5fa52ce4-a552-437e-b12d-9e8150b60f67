"""
Notification service for managing user notifications.
"""
from typing import List, <PERSON><PERSON>, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from fastapi import HTTPException, status
from datetime import datetime
import json

from app.models.notification import (
    Notification, NotificationTemplate, NotificationType, 
    NotificationChannel, NotificationPriority
)
from app.models.user import User
from app.services.base_service import BaseService


class NotificationService(BaseService[Notification]):
    """Service for managing notifications."""
    
    def __init__(self, db: Session):
        super().__init__(Notification, db)
    
    def create_notification(
        self,
        user_id: int,
        title: str,
        message: str,
        notification_type: NotificationType,
        channels: List[NotificationChannel] = None,
        priority: NotificationPriority = NotificationPriority.NORMAL,
        related_promotion_id: Optional[int] = None,
        related_merchant_id: Optional[int] = None,
        action_url: Optional[str] = None,
        action_text: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Notification:
        """
        Create a new notification.
        
        Args:
            user_id: Target user ID
            title: Notification title
            message: Notification message
            notification_type: Type of notification
            channels: Delivery channels
            priority: Notification priority
            related_promotion_id: Related promotion ID
            related_merchant_id: Related merchant ID
            action_url: Action URL
            action_text: Action button text
            metadata: Additional metadata
            
        Returns:
            Created notification
        """
        if channels is None:
            channels = [NotificationChannel.IN_APP]
        
        # Convert channels list to comma-separated string
        channels_str = ",".join([channel.value for channel in channels])
        
        # Convert metadata to JSON string
        metadata_str = json.dumps(metadata) if metadata else None
        
        notification = Notification(
            user_id=user_id,
            title=title,
            message=message,
            notification_type=notification_type,
            priority=priority,
            channels=channels_str,
            related_promotion_id=related_promotion_id,
            related_merchant_id=related_merchant_id,
            action_url=action_url,
            action_text=action_text,
            metadata=metadata_str
        )
        
        self.db.add(notification)
        self.db.commit()
        self.db.refresh(notification)
        
        # TODO: Send notification via configured channels
        # self._send_notification(notification)
        
        return notification
    
    def create_from_template(
        self,
        user_id: int,
        template_name: str,
        template_data: Dict[str, Any] = None,
        related_promotion_id: Optional[int] = None,
        related_merchant_id: Optional[int] = None,
        language: str = "en"
    ) -> Notification:
        """
        Create notification from template.
        
        Args:
            user_id: Target user ID
            template_name: Template name
            template_data: Data for template rendering
            related_promotion_id: Related promotion ID
            related_merchant_id: Related merchant ID
            language: User's preferred language
            
        Returns:
            Created notification
            
        Raises:
            HTTPException: If template not found
        """
        template = self.db.query(NotificationTemplate).filter(
            NotificationTemplate.name == template_name
        ).first()
        
        if not template:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Notification template '{template_name}' not found"
            )
        
        # Get localized templates
        title_template = template.get_title_template(language)
        message_template = template.get_message_template(language)
        
        # Render templates with data
        if template_data:
            title = title_template.format(**template_data)
            message = message_template.format(**template_data)
        else:
            title = title_template
            message = message_template
        
        # Parse default channels
        channels = [
            NotificationChannel(channel.strip()) 
            for channel in template.default_channels.split(",")
        ]
        
        return self.create_notification(
            user_id=user_id,
            title=title,
            message=message,
            notification_type=template.notification_type,
            channels=channels,
            priority=template.default_priority,
            related_promotion_id=related_promotion_id,
            related_merchant_id=related_merchant_id
        )
    
    def get_user_notifications(
        self,
        user_id: int,
        unread_only: bool = False,
        notification_type: Optional[NotificationType] = None,
        skip: int = 0,
        limit: int = 50
    ) -> Tuple[List[Notification], int]:
        """
        Get user's notifications.
        
        Args:
            user_id: User ID
            unread_only: Only return unread notifications
            notification_type: Filter by notification type
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            Tuple of (notifications, total_count)
        """
        query = self.db.query(Notification).filter(
            and_(
                Notification.user_id == user_id,
                Notification.is_active == True
            )
        )
        
        if unread_only:
            query = query.filter(Notification.is_read == False)
        
        if notification_type:
            query = query.filter(Notification.notification_type == notification_type)
        
        query = query.order_by(Notification.created_at.desc())
        
        total = query.count()
        notifications = query.offset(skip).limit(limit).all()
        
        return notifications, total
    
    def mark_as_read(self, notification_id: int, user_id: int) -> bool:
        """
        Mark notification as read.
        
        Args:
            notification_id: Notification ID
            user_id: User ID (for authorization)
            
        Returns:
            True if marked as read
            
        Raises:
            HTTPException: If notification not found or unauthorized
        """
        notification = self.db.query(Notification).filter(
            and_(
                Notification.id == notification_id,
                Notification.user_id == user_id,
                Notification.is_active == True
            )
        ).first()
        
        if not notification:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Notification not found"
            )
        
        if not notification.is_read:
            notification.is_read = True
            notification.read_at = datetime.utcnow()
            self.db.commit()
        
        return True
    
    def mark_all_as_read(self, user_id: int) -> int:
        """
        Mark all user's notifications as read.
        
        Args:
            user_id: User ID
            
        Returns:
            Number of notifications marked as read
        """
        count = self.db.query(Notification).filter(
            and_(
                Notification.user_id == user_id,
                Notification.is_read == False,
                Notification.is_active == True
            )
        ).update({
            Notification.is_read: True,
            Notification.read_at: datetime.utcnow()
        })
        
        self.db.commit()
        return count
    
    def get_unread_count(self, user_id: int) -> int:
        """
        Get count of unread notifications for user.
        
        Args:
            user_id: User ID
            
        Returns:
            Number of unread notifications
        """
        return self.db.query(Notification).filter(
            and_(
                Notification.user_id == user_id,
                Notification.is_read == False,
                Notification.is_active == True
            )
        ).count()
    
    def delete_notification(self, notification_id: int, user_id: int) -> bool:
        """
        Delete (soft delete) a notification.
        
        Args:
            notification_id: Notification ID
            user_id: User ID (for authorization)
            
        Returns:
            True if deleted
            
        Raises:
            HTTPException: If notification not found or unauthorized
        """
        notification = self.db.query(Notification).filter(
            and_(
                Notification.id == notification_id,
                Notification.user_id == user_id,
                Notification.is_active == True
            )
        ).first()
        
        if not notification:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Notification not found"
            )
        
        notification.is_active = False
        self.db.commit()
        
        return True
    
    def broadcast_notification(
        self,
        title: str,
        message: str,
        notification_type: NotificationType,
        user_filter: Optional[Dict[str, Any]] = None,
        channels: List[NotificationChannel] = None,
        priority: NotificationPriority = NotificationPriority.NORMAL
    ) -> int:
        """
        Send notification to multiple users.
        
        Args:
            title: Notification title
            message: Notification message
            notification_type: Type of notification
            user_filter: Filter criteria for users
            channels: Delivery channels
            priority: Notification priority
            
        Returns:
            Number of notifications created
        """
        # Get target users
        query = self.db.query(User).filter(User.is_active == True)
        
        if user_filter:
            # Apply user filters (role, location, etc.)
            for key, value in user_filter.items():
                if hasattr(User, key):
                    query = query.filter(getattr(User, key) == value)
        
        users = query.all()
        
        # Create notifications for each user
        notifications = []
        for user in users:
            notification = Notification(
                user_id=user.id,
                title=title,
                message=message,
                notification_type=notification_type,
                priority=priority,
                channels=",".join([ch.value for ch in (channels or [NotificationChannel.IN_APP])])
            )
            notifications.append(notification)
        
        self.db.add_all(notifications)
        self.db.commit()
        
        return len(notifications)
