"""
Tests for authentication endpoints.
"""
import pytest
from fastapi.testclient import Test<PERSON>lient


def test_user_registration(client: TestClient):
    """Test user registration."""
    user_data = {
        "email": "<EMAIL>",
        "username": "newuser",
        "password": "NewPassword123!",
        "confirm_password": "NewPassword123!",
        "first_name": "New",
        "last_name": "User"
    }
    
    response = client.post("/api/v1/auth/register", json=user_data)
    assert response.status_code == 201
    
    data = response.json()
    assert data["email"] == user_data["email"]
    assert data["username"] == user_data["username"]
    assert data["first_name"] == user_data["first_name"]
    assert data["last_name"] == user_data["last_name"]
    assert "hashed_password" not in data


def test_user_registration_duplicate_email(client: TestClient, test_user):
    """Test user registration with duplicate email."""
    user_data = {
        "email": test_user.email,
        "username": "differentuser",
        "password": "NewPassword123!",
        "confirm_password": "NewPassword123!",
        "first_name": "Different",
        "last_name": "User"
    }
    
    response = client.post("/api/v1/auth/register", json=user_data)
    assert response.status_code == 400
    assert "Email already registered" in response.json()["detail"]


def test_user_registration_duplicate_username(client: TestClient, test_user):
    """Test user registration with duplicate username."""
    user_data = {
        "email": "<EMAIL>",
        "username": test_user.username,
        "password": "NewPassword123!",
        "confirm_password": "NewPassword123!",
        "first_name": "Different",
        "last_name": "User"
    }
    
    response = client.post("/api/v1/auth/register", json=user_data)
    assert response.status_code == 400
    assert "Username already taken" in response.json()["detail"]


def test_user_registration_password_mismatch(client: TestClient):
    """Test user registration with password mismatch."""
    user_data = {
        "email": "<EMAIL>",
        "username": "mismatchuser",
        "password": "Password123!",
        "confirm_password": "DifferentPassword123!",
        "first_name": "Mismatch",
        "last_name": "User"
    }
    
    response = client.post("/api/v1/auth/register", json=user_data)
    assert response.status_code == 422


def test_user_login_success(client: TestClient, test_user):
    """Test successful user login."""
    login_data = {
        "email": test_user.email,
        "password": "testpassword"
    }
    
    response = client.post("/api/v1/auth/login", json=login_data)
    assert response.status_code == 200
    
    data = response.json()
    assert "access_token" in data
    assert "refresh_token" in data
    assert data["token_type"] == "bearer"
    assert "expires_in" in data


def test_user_login_invalid_email(client: TestClient):
    """Test login with invalid email."""
    login_data = {
        "email": "<EMAIL>",
        "password": "testpassword"
    }
    
    response = client.post("/api/v1/auth/login", json=login_data)
    assert response.status_code == 401
    assert "Incorrect email or password" in response.json()["detail"]


def test_user_login_invalid_password(client: TestClient, test_user):
    """Test login with invalid password."""
    login_data = {
        "email": test_user.email,
        "password": "wrongpassword"
    }
    
    response = client.post("/api/v1/auth/login", json=login_data)
    assert response.status_code == 401
    assert "Incorrect email or password" in response.json()["detail"]


def test_get_current_user(client: TestClient, auth_headers):
    """Test getting current user information."""
    response = client.get("/api/v1/auth/me", headers=auth_headers)
    assert response.status_code == 200
    
    data = response.json()
    assert "id" in data
    assert "email" in data
    assert "username" in data
    assert "role" in data


def test_get_current_user_unauthorized(client: TestClient):
    """Test getting current user without authentication."""
    response = client.get("/api/v1/auth/me")
    assert response.status_code == 401


def test_get_current_user_invalid_token(client: TestClient):
    """Test getting current user with invalid token."""
    headers = {"Authorization": "Bearer invalid_token"}
    response = client.get("/api/v1/auth/me", headers=headers)
    assert response.status_code == 401


def test_refresh_token(client: TestClient, test_user):
    """Test token refresh."""
    # First login to get tokens
    login_data = {
        "email": test_user.email,
        "password": "testpassword"
    }
    
    login_response = client.post("/api/v1/auth/login", json=login_data)
    refresh_token = login_response.json()["refresh_token"]
    
    # Use refresh token to get new access token
    refresh_data = {
        "refresh_token": refresh_token
    }
    
    response = client.post("/api/v1/auth/refresh", json=refresh_data)
    assert response.status_code == 200
    
    data = response.json()
    assert "access_token" in data
    assert "refresh_token" in data
    assert data["token_type"] == "bearer"


def test_refresh_token_invalid(client: TestClient):
    """Test token refresh with invalid token."""
    refresh_data = {
        "refresh_token": "invalid_refresh_token"
    }
    
    response = client.post("/api/v1/auth/refresh", json=refresh_data)
    assert response.status_code == 401


def test_logout(client: TestClient, auth_headers):
    """Test user logout."""
    response = client.post("/api/v1/auth/logout", headers=auth_headers)
    assert response.status_code == 200
    assert "Successfully logged out" in response.json()["message"]
