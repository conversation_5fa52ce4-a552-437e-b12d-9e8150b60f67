"""
Favorites management endpoints.
"""
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.deps import get_current_user
from app.schemas.promotion import PromotionSummary, PromotionList
from app.services.favorite_service import FavoriteService
from app.models.user import User

router = APIRouter()


@router.post("/{promotion_id}")
async def add_to_favorites(
    promotion_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Add a promotion to user's favorites.
    """
    favorite_service = FavoriteService(db)
    favorite = favorite_service.add_favorite(current_user.id, promotion_id)
    
    return {
        "message": "Promotion added to favorites",
        "favorite_id": favorite.id,
        "promotion_id": promotion_id
    }


@router.delete("/{promotion_id}")
async def remove_from_favorites(
    promotion_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Remove a promotion from user's favorites.
    """
    favorite_service = FavoriteService(db)
    success = favorite_service.remove_favorite(current_user.id, promotion_id)
    
    if success:
        return {
            "message": "Promotion removed from favorites",
            "promotion_id": promotion_id
        }
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Failed to remove from favorites"
        )


@router.get("/", response_model=PromotionList)
async def get_my_favorites(
    page: int = Query(1, ge=1),
    per_page: int = Query(20, ge=1, le=100),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get user's favorite promotions.
    """
    favorite_service = FavoriteService(db)
    
    skip = (page - 1) * per_page
    promotions, total = favorite_service.get_user_favorites(
        current_user.id, 
        skip=skip, 
        limit=per_page
    )
    
    pages = (total + per_page - 1) // per_page
    
    return PromotionList(
        promotions=promotions,
        total=total,
        page=page,
        per_page=per_page,
        pages=pages
    )


@router.get("/check/{promotion_id}")
async def check_favorite_status(
    promotion_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Check if a promotion is in user's favorites.
    """
    favorite_service = FavoriteService(db)
    is_favorited = favorite_service.is_favorited(current_user.id, promotion_id)
    
    return {
        "promotion_id": promotion_id,
        "is_favorited": is_favorited
    }


@router.post("/check-multiple")
async def check_multiple_favorites(
    promotion_ids: List[int],
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Check favorite status for multiple promotions.
    """
    if len(promotion_ids) > 100:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Maximum 100 promotion IDs allowed"
        )
    
    favorite_service = FavoriteService(db)
    favorites_status = favorite_service.bulk_check_favorites(
        current_user.id, 
        promotion_ids
    )
    
    return {
        "favorites": favorites_status
    }


@router.get("/count")
async def get_favorites_count(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get total number of user's favorites.
    """
    favorite_service = FavoriteService(db)
    count = favorite_service.get_favorite_count(current_user.id)
    
    return {
        "user_id": current_user.id,
        "favorites_count": count
    }


@router.get("/popular", response_model=List[PromotionSummary])
async def get_popular_promotions(
    limit: int = Query(10, ge=1, le=50),
    category_id: int = Query(None),
    db: Session = Depends(get_db)
):
    """
    Get most favorited promotions.
    """
    favorite_service = FavoriteService(db)
    promotions = favorite_service.get_popular_promotions(
        limit=limit,
        category_id=category_id
    )
    
    return promotions
