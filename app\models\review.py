"""
Review and feedback models.
"""
from sqlalchemy import Column, String, Text, Integer, Float, Boolean, ForeignKey, Enum
from sqlalchemy.orm import relationship
import enum

from app.models.base import BaseModel


class ReviewStatus(str, enum.Enum):
    """Review status enumeration."""
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    FLAGGED = "flagged"


class Review(BaseModel):
    """Review model for promotions and merchants."""
    __tablename__ = "reviews"
    
    # User and Target
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    promotion_id = Column(Integer, ForeignKey("promotions.id"), nullable=True)
    merchant_id = Column(Integer, ForeignKey("merchants.id"), nullable=True)
    
    # Review Content
    rating = Column(Float, nullable=False)  # 1-5 stars
    title = Column(String(200), nullable=True)
    comment = Column(Text, nullable=True)
    
    # Status and Moderation
    status = Column(Enum(ReviewStatus), default=ReviewStatus.PENDING, nullable=False)
    is_verified_purchase = Column(Boolean, default=False, nullable=False)
    
    # Helpfulness
    helpful_count = Column(Integer, default=0, nullable=False)
    not_helpful_count = Column(Integer, default=0, nullable=False)
    
    # Moderation
    moderated_by = Column(Integer, ForeignKey("users.id"), nullable=True)
    moderation_reason = Column(Text, nullable=True)
    
    # Relationships
    user = relationship("User", back_populates="reviews", foreign_keys=[user_id])
    promotion = relationship("Promotion", back_populates="reviews")
    merchant = relationship("Merchant", back_populates="reviews")
    moderator = relationship("User", foreign_keys=[moderated_by])


class ReviewHelpfulness(BaseModel):
    """Track review helpfulness votes."""
    __tablename__ = "review_helpfulness"
    
    review_id = Column(Integer, ForeignKey("reviews.id"), nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    is_helpful = Column(Boolean, nullable=False)
    
    # Relationships
    review = relationship("Review")
    user = relationship("User")
