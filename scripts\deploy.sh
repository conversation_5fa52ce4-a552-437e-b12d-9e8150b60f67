#!/bin/bash

# Promodetect API Deployment Script
# This script deploys the application to the production server with zero-downtime

set -e  # Exit on any error

# Configuration
BACKEND_SERVER="**************"
DB_SERVER="**************"
DEPLOY_USER="${DEPLOY_USER:-deploy}"
APP_NAME="promodetect-api"
DEPLOY_PATH="/opt/promodetect"
CONTAINER_NAME="${APP_NAME}-app"
NGINX_CONTAINER="${APP_NAME}-nginx"
BACKUP_CONTAINER="${APP_NAME}-backup"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a deployment.log
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a deployment.log
    exit 1
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a deployment.log
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a deployment.log
}

# Function to check if command exists on remote server
check_remote_command() {
    ssh $DEPLOY_USER@$BACKEND_SERVER "command -v $1 >/dev/null 2>&1" || {
        error "Command '$1' not found on remote server"
    }
}

# Function to backup current deployment
backup_current_deployment() {
    log "Creating backup of current deployment..."
    
    ssh $DEPLOY_USER@$BACKEND_SERVER << 'EOF'
        if docker ps -q -f name=promodetect-api-app; then
            # Create backup tag for current running image
            CURRENT_IMAGE=$(docker inspect promodetect-api-app --format='{{.Config.Image}}')
            docker tag $CURRENT_IMAGE promodetect-api:backup-$(date +%Y%m%d-%H%M%S)
            echo "Backup created for image: $CURRENT_IMAGE"
        else
            echo "No running container found to backup"
        fi
EOF
    
    success "Backup completed"
}

# Function to create deployment directory structure
setup_deployment_structure() {
    log "Setting up deployment directory structure..."
    
    ssh $DEPLOY_USER@$BACKEND_SERVER << EOF
        # Create deployment directories
        sudo mkdir -p $DEPLOY_PATH/{config,logs,uploads,backups}
        sudo chown -R $DEPLOY_USER:$DEPLOY_USER $DEPLOY_PATH
        
        # Create logs directory with proper permissions
        mkdir -p $DEPLOY_PATH/logs
        chmod 755 $DEPLOY_PATH/logs
        
        # Create uploads directory
        mkdir -p $DEPLOY_PATH/uploads/{images,avatars,promotions,merchants}
        chmod 755 $DEPLOY_PATH/uploads
EOF
    
    success "Deployment structure created"
}

# Function to create production environment file
create_env_file() {
    log "Creating production environment file..."
    
    ssh $DEPLOY_USER@$BACKEND_SERVER << EOF
        cat > $DEPLOY_PATH/config/.env << 'ENVEOF'
# Production Environment Configuration
DEBUG=False
ENVIRONMENT=production

# Database Configuration
DATABASE_HOST=$DB_SERVER
DATABASE_PORT=5432
DATABASE_NAME=$DATABASE_NAME
DATABASE_USER=$DATABASE_USER
DATABASE_PASSWORD=$DATABASE_PASSWORD
DATABASE_URL=**************************************************************/$DATABASE_NAME

# JWT Configuration
SECRET_KEY=$JWT_SECRET_KEY
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# API Configuration
API_V1_STR=/api/v1
PROJECT_NAME=Promodetect API
PROJECT_VERSION=1.0.0

# CORS Configuration
BACKEND_CORS_ORIGINS=["http://**************", "https://promodetect.com", "https://api.promodetect.com"]

# Redis Configuration
REDIS_URL=redis://redis:6379/0

# File Upload Configuration
UPLOAD_DIR=/app/uploads
MAX_FILE_SIZE=10485760
ALLOWED_IMAGE_EXTENSIONS=jpg,jpeg,png,gif,webp

# Email Configuration
MAIL_USERNAME=$MAIL_USERNAME
MAIL_PASSWORD=$MAIL_PASSWORD
MAIL_FROM=$MAIL_FROM
MAIL_PORT=587
MAIL_SERVER=$MAIL_SERVER
MAIL_TLS=True
MAIL_SSL=False

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json

# Celery Configuration
CELERY_BROKER_URL=redis://redis:6379/1
CELERY_RESULT_BACKEND=redis://redis:6379/1

# Internationalization
DEFAULT_LANGUAGE=en
SUPPORTED_LANGUAGES=en,fr,ar
ENVEOF

        chmod 600 $DEPLOY_PATH/config/.env
EOF
    
    success "Environment file created"
}

# Function to create docker-compose production file
create_docker_compose() {
    log "Creating production docker-compose file..."
    
    ssh $DEPLOY_USER@$BACKEND_SERVER << 'EOF'
        cat > /opt/promodetect/docker-compose.prod.yml << 'COMPOSEEOF'
version: '3.8'

services:
  # Redis for caching and rate limiting
  redis:
    image: redis:7-alpine
    container_name: promodetect-api-redis
    restart: unless-stopped
    volumes:
      - redis_data:/data
    networks:
      - promodetect-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # FastAPI Application
  api:
    image: ${CI_REGISTRY_IMAGE}:${CI_COMMIT_SHA}
    container_name: promodetect-api-app
    restart: unless-stopped
    env_file:
      - ./config/.env
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - promodetect-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: promodetect-api-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./config/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./uploads:/var/www/uploads:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - api
    networks:
      - promodetect-network

volumes:
  redis_data:

networks:
  promodetect-network:
    driver: bridge
COMPOSEEOF
EOF
    
    success "Docker Compose file created"
}

# Function to create nginx configuration
create_nginx_config() {
    log "Creating Nginx configuration..."
    
    ssh $DEPLOY_USER@$BACKEND_SERVER << 'EOF'
        cat > /opt/promodetect/config/nginx.conf << 'NGINXEOF'
events {
    worker_connections 1024;
}

http {
    upstream api {
        server api:8000;
    }

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=auth:10m rate=5r/s;

    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";

    server {
        listen 80;
        server_name **************;

        # Redirect HTTP to HTTPS (uncomment when SSL is configured)
        # return 301 https://$server_name$request_uri;

        # API endpoints
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://api;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }

        # Authentication endpoints with stricter rate limiting
        location /api/v1/auth/ {
            limit_req zone=auth burst=10 nodelay;
            proxy_pass http://api;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Health check
        location /health {
            proxy_pass http://api;
            access_log off;
        }

        # Serve uploaded files
        location /uploads/ {
            alias /var/www/uploads/;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # Documentation
        location /docs {
            proxy_pass http://api;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Default location
        location / {
            proxy_pass http://api;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
NGINXEOF
EOF
    
    success "Nginx configuration created"
}

# Function to pull latest Docker image
pull_docker_image() {
    log "Pulling latest Docker image..."
    
    ssh $DEPLOY_USER@$BACKEND_SERVER << EOF
        # Login to GitLab Container Registry
        echo $CI_REGISTRY_PASSWORD | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY
        
        # Pull the latest image
        docker pull $CONTAINER_IMAGE
        
        # Tag as latest for easier management
        docker tag $CONTAINER_IMAGE $CI_REGISTRY_IMAGE:latest
EOF
    
    success "Docker image pulled successfully"
}

# Function to run database migrations
run_migrations() {
    log "Running database migrations..."
    
    ssh $DEPLOY_USER@$BACKEND_SERVER << EOF
        cd $DEPLOY_PATH
        
        # Run migrations using a temporary container
        docker run --rm \
            --env-file ./config/.env \
            --network host \
            $CONTAINER_IMAGE \
            alembic upgrade head
EOF
    
    success "Database migrations completed"
}

# Function to deploy with zero downtime
deploy_zero_downtime() {
    log "Starting zero-downtime deployment..."
    
    ssh $DEPLOY_USER@$BACKEND_SERVER << EOF
        cd $DEPLOY_PATH
        
        # Export environment variables for docker-compose
        export CI_REGISTRY_IMAGE=$CI_REGISTRY_IMAGE
        export CI_COMMIT_SHA=$CI_COMMIT_SHA
        
        # Start new containers
        docker-compose -f docker-compose.prod.yml up -d --no-deps api
        
        # Wait for health check
        echo "Waiting for application to be healthy..."
        for i in {1..30}; do
            if docker exec promodetect-api-app curl -f http://localhost:8000/health >/dev/null 2>&1; then
                echo "Application is healthy"
                break
            fi
            if [ \$i -eq 30 ]; then
                echo "Health check failed after 30 attempts"
                exit 1
            fi
            sleep 2
        done
        
        # Update nginx if needed
        docker-compose -f docker-compose.prod.yml up -d nginx
        
        # Clean up old containers
        docker container prune -f
EOF
    
    success "Zero-downtime deployment completed"
}

# Main deployment function
main() {
    log "Starting deployment of Promodetect API to production..."
    log "Target server: $BACKEND_SERVER"
    log "Database server: $DB_SERVER"
    log "Container image: $CONTAINER_IMAGE"
    
    # Pre-deployment checks
    log "Performing pre-deployment checks..."
    check_remote_command "docker"
    check_remote_command "docker-compose"
    
    # Backup current deployment
    backup_current_deployment
    
    # Setup deployment structure
    setup_deployment_structure
    
    # Create configuration files
    create_env_file
    create_docker_compose
    create_nginx_config
    
    # Pull latest image
    pull_docker_image
    
    # Run database migrations
    run_migrations
    
    # Deploy with zero downtime
    deploy_zero_downtime
    
    success "Deployment completed successfully!"
    log "Application is now running at: http://$BACKEND_SERVER"
    log "API Documentation: http://$BACKEND_SERVER/api/v1/docs"
}

# Error handling
trap 'error "Deployment failed at line $LINENO"' ERR

# Run main deployment
main "$@"
