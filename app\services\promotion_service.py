"""
Promotion service for business logic.
"""
from typing import Optional, List, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, text
from fastapi import HTTPException, status
from datetime import datetime
import math

from app.models.promotion import Promotion, PromotionStatus
from app.models.merchant import Merchant, MerchantLocation
from app.models.category import Category
from app.schemas.promotion import PromotionCreate, PromotionUpdate, PromotionSearch, PromotionStats
from app.services.base_service import BaseService


class PromotionService(BaseService[Promotion]):
    """Promotion service for managing promotion operations."""
    
    def __init__(self, db: Session):
        super().__init__(Promotion, db)
    
    def create_promotion(self, promotion_data: PromotionCreate, merchant_id: int) -> Promotion:
        """
        Create a new promotion.
        
        Args:
            promotion_data: Promotion creation data
            merchant_id: ID of the merchant creating the promotion
            
        Returns:
            Created promotion
            
        Raises:
            HTTPException: If merchant or category not found
        """
        # Verify merchant exists
        merchant = self.db.query(Merchant).filter(Merchant.id == merchant_id).first()
        if not merchant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Merchant not found"
            )
        
        # Verify category exists
        category = self.db.query(Category).filter(Category.id == promotion_data.category_id).first()
        if not category:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Category not found"
            )
        
        # Verify location if provided
        if promotion_data.location_id:
            location = self.db.query(MerchantLocation).filter(
                and_(
                    MerchantLocation.id == promotion_data.location_id,
                    MerchantLocation.merchant_id == merchant_id
                )
            ).first()
            if not location:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Location not found or doesn't belong to merchant"
                )
        
        # Generate slug
        slug = self._generate_slug(promotion_data.title)
        
        # Create promotion
        promotion = Promotion(
            **promotion_data.dict(),
            merchant_id=merchant_id,
            slug=slug,
            status=PromotionStatus.DRAFT
        )
        
        self.db.add(promotion)
        self.db.commit()
        self.db.refresh(promotion)
        
        return promotion
    
    def update_promotion(self, promotion_id: int, promotion_data: PromotionUpdate, merchant_id: int = None) -> Promotion:
        """
        Update a promotion.
        
        Args:
            promotion_id: Promotion ID
            promotion_data: Promotion update data
            merchant_id: ID of the merchant (for authorization)
            
        Returns:
            Updated promotion
            
        Raises:
            HTTPException: If promotion not found or unauthorized
        """
        promotion = self.get_or_404(promotion_id)
        
        # Check authorization if merchant_id provided
        if merchant_id and promotion.merchant_id != merchant_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to update this promotion"
            )
        
        # Update fields
        for field, value in promotion_data.dict(exclude_unset=True).items():
            if hasattr(promotion, field):
                setattr(promotion, field, value)
        
        # Update slug if title changed
        if promotion_data.title:
            promotion.slug = self._generate_slug(promotion_data.title)
        
        promotion.updated_at = datetime.utcnow()
        self.db.commit()
        self.db.refresh(promotion)
        
        return promotion
    
    def search_promotions(self, search_params: PromotionSearch) -> Tuple[List[Promotion], int]:
        """
        Search promotions with filters and pagination.
        
        Args:
            search_params: Search parameters
            
        Returns:
            Tuple of (promotions, total_count)
        """
        query = self.db.query(Promotion).filter(Promotion.is_active == True)
        
        # Apply filters
        if search_params.query:
            query = query.filter(
                or_(
                    Promotion.title.ilike(f"%{search_params.query}%"),
                    Promotion.description.ilike(f"%{search_params.query}%"),
                    Promotion.short_description.ilike(f"%{search_params.query}%")
                )
            )
        
        if search_params.category_id:
            query = query.filter(Promotion.category_id == search_params.category_id)
        
        if search_params.merchant_id:
            query = query.filter(Promotion.merchant_id == search_params.merchant_id)
        
        if search_params.promotion_type:
            query = query.filter(Promotion.promotion_type == search_params.promotion_type)
        
        if search_params.status:
            query = query.filter(Promotion.status == search_params.status)
        else:
            # Default to active promotions only
            query = query.filter(Promotion.status == PromotionStatus.ACTIVE)
        
        if search_params.is_featured is not None:
            query = query.filter(Promotion.is_featured == search_params.is_featured)
        
        if search_params.is_exclusive is not None:
            query = query.filter(Promotion.is_exclusive == search_params.is_exclusive)
        
        if search_params.min_discount is not None:
            query = query.filter(Promotion.discount_value >= search_params.min_discount)
        
        if search_params.max_discount is not None:
            query = query.filter(Promotion.discount_value <= search_params.max_discount)
        
        # Date filters
        if search_params.start_date_from:
            query = query.filter(Promotion.start_date >= search_params.start_date_from)
        
        if search_params.start_date_to:
            query = query.filter(Promotion.start_date <= search_params.start_date_to)
        
        if search_params.end_date_from:
            query = query.filter(Promotion.end_date >= search_params.end_date_from)
        
        if search_params.end_date_to:
            query = query.filter(Promotion.end_date <= search_params.end_date_to)
        
        # Location-based filtering
        if search_params.location_lat and search_params.location_lng and search_params.radius_km:
            query = self._apply_location_filter(
                query, 
                search_params.location_lat, 
                search_params.location_lng, 
                search_params.radius_km
            )
        
        # Get total count
        total = query.count()
        
        # Apply sorting
        if search_params.sort_by == "created_at":
            order_field = Promotion.created_at
        elif search_params.sort_by == "start_date":
            order_field = Promotion.start_date
        elif search_params.sort_by == "end_date":
            order_field = Promotion.end_date
        elif search_params.sort_by == "view_count":
            order_field = Promotion.view_count
        elif search_params.sort_by == "favorite_count":
            order_field = Promotion.favorite_count
        elif search_params.sort_by == "discount_value":
            order_field = Promotion.discount_value
        else:
            order_field = Promotion.created_at
        
        if search_params.sort_order == "asc":
            query = query.order_by(order_field.asc())
        else:
            query = query.order_by(order_field.desc())
        
        # Apply pagination
        offset = (search_params.page - 1) * search_params.per_page
        promotions = query.offset(offset).limit(search_params.per_page).all()
        
        return promotions, total
    
    def get_merchant_promotions(self, merchant_id: int, skip: int = 0, limit: int = 100) -> List[Promotion]:
        """
        Get promotions for a specific merchant.
        
        Args:
            merchant_id: Merchant ID
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of promotions
        """
        return self.db.query(Promotion).filter(
            and_(
                Promotion.merchant_id == merchant_id,
                Promotion.is_active == True
            )
        ).offset(skip).limit(limit).all()
    
    def increment_view_count(self, promotion_id: int) -> bool:
        """
        Increment promotion view count.
        
        Args:
            promotion_id: Promotion ID
            
        Returns:
            True if successful
        """
        result = self.db.query(Promotion).filter(Promotion.id == promotion_id).update(
            {Promotion.view_count: Promotion.view_count + 1}
        )
        self.db.commit()
        return result > 0
    
    def increment_click_count(self, promotion_id: int) -> bool:
        """
        Increment promotion click count.
        
        Args:
            promotion_id: Promotion ID
            
        Returns:
            True if successful
        """
        result = self.db.query(Promotion).filter(Promotion.id == promotion_id).update(
            {Promotion.click_count: Promotion.click_count + 1}
        )
        self.db.commit()
        return result > 0
    
    def get_promotion_stats(self, merchant_id: int = None) -> PromotionStats:
        """
        Get promotion statistics.
        
        Args:
            merchant_id: Optional merchant ID to filter stats
            
        Returns:
            Promotion statistics
        """
        query = self.db.query(Promotion).filter(Promotion.is_active == True)
        
        if merchant_id:
            query = query.filter(Promotion.merchant_id == merchant_id)
        
        total_promotions = query.count()
        active_promotions = query.filter(Promotion.status == PromotionStatus.ACTIVE).count()
        expired_promotions = query.filter(Promotion.status == PromotionStatus.EXPIRED).count()
        draft_promotions = query.filter(Promotion.status == PromotionStatus.DRAFT).count()
        
        # Aggregate stats
        stats = query.with_entities(
            func.sum(Promotion.view_count).label('total_views'),
            func.sum(Promotion.click_count).label('total_clicks'),
            func.sum(Promotion.conversion_count).label('total_conversions'),
            func.sum(Promotion.favorite_count).label('total_favorites')
        ).first()
        
        total_views = stats.total_views or 0
        total_clicks = stats.total_clicks or 0
        total_conversions = stats.total_conversions or 0
        total_favorites = stats.total_favorites or 0
        
        # Calculate conversion rate
        avg_conversion_rate = (total_conversions / total_clicks * 100) if total_clicks > 0 else 0
        
        return PromotionStats(
            total_promotions=total_promotions,
            active_promotions=active_promotions,
            expired_promotions=expired_promotions,
            draft_promotions=draft_promotions,
            total_views=total_views,
            total_clicks=total_clicks,
            total_conversions=total_conversions,
            total_favorites=total_favorites,
            avg_conversion_rate=round(avg_conversion_rate, 2)
        )
    
    def _generate_slug(self, title: str) -> str:
        """Generate URL slug from title."""
        import re
        slug = re.sub(r'[^\w\s-]', '', title.lower())
        slug = re.sub(r'[-\s]+', '-', slug)
        return slug.strip('-')
    
    def _apply_location_filter(self, query, lat: float, lng: float, radius_km: float):
        """Apply location-based filtering using Haversine formula."""
        # This is a simplified version - in production, you'd want to use PostGIS or similar
        # for better performance with spatial queries
        earth_radius = 6371  # Earth's radius in kilometers
        
        # Convert to radians
        lat_rad = math.radians(lat)
        lng_rad = math.radians(lng)
        
        # Haversine formula in SQL
        distance_formula = func.acos(
            func.sin(lat_rad) * func.sin(func.radians(MerchantLocation.latitude)) +
            func.cos(lat_rad) * func.cos(func.radians(MerchantLocation.latitude)) *
            func.cos(func.radians(MerchantLocation.longitude) - lng_rad)
        ) * earth_radius
        
        # Join with merchant locations and filter by distance
        query = query.join(MerchantLocation, Promotion.location_id == MerchantLocation.id)
        query = query.filter(distance_formula <= radius_km)
        
        return query
