# GitLab CI/CD Pipeline for Promodetect API
# This pipeline builds, tests, and deploys the FastAPI application

stages:
  - build
  - test
  - deploy
  - post-deploy

variables:
  # Docker configuration
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: "/certs"
  CONTAINER_IMAGE: $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA
  LATEST_IMAGE: $CI_REGISTRY_IMAGE:latest
  
  # Application configuration
  APP_NAME: promodetect-api
  DEPLOY_PATH: /opt/promodetect
  
  # Database configuration
  DATABASE_HOST: "**************"
  DATABASE_PORT: "5432"
  DATABASE_NAME: "promodetect_db"

# Build stage - Create Docker image
build:
  stage: build
  image: docker:20.10.16
  services:
    - docker:20.10.16-dind
  tags:
    - PROMODETECT
  before_script:
    - echo $CI_REGISTRY_PASSWORD | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY
  script:
    - echo "Building Docker image..."
    - docker build -t $CONTAINER_IMAGE -t $LATEST_IMAGE .
    - echo "Pushing Docker image to registry..."
    - docker push $CONTAINER_IMAGE
    - docker push $LATEST_IMAGE
    - echo "Docker image built and pushed successfully"
  only:
    - main
    - master
  artifacts:
    reports:
      dotenv: build.env
    expire_in: 1 hour

# Test stage - Run tests and quality checks
test:
  stage: test
  image: python:3.11-slim
  tags:
    - PROMODETECT
  services:
    - postgres:15
    - redis:7-alpine
  variables:
    POSTGRES_DB: test_promodetect_db
    POSTGRES_USER: test_user
    POSTGRES_PASSWORD: test_password
    DATABASE_URL: **************************************************/test_promodetect_db
    REDIS_URL: redis://redis:6379/0
    SECRET_KEY: test-secret-key-for-ci
  before_script:
    - apt-get update -qy
    - apt-get install -y gcc libpq-dev
    - pip install --upgrade pip
    - pip install -r requirements.txt
    - pip install pytest-cov flake8 black isort bandit safety
  script:
    - echo "Running code quality checks..."
    - black --check app/
    - isort --check-only app/
    - flake8 app/ --max-line-length=100 --exclude=__pycache__
    - echo "Running security checks..."
    - bandit -r app/ -f json -o bandit-report.json || true
    - safety check --json --output safety-report.json || true
    - echo "Running tests..."
    - pytest --cov=app --cov-report=xml --cov-report=term --junitxml=pytest-report.xml -v
    - echo "All tests and quality checks passed"
  coverage: '/TOTAL.*\s+(\d+%)$/'
  artifacts:
    reports:
      junit: pytest-report.xml
      coverage_report:
        coverage_format: cobertura
        path: coverage.xml
    paths:
      - bandit-report.json
      - safety-report.json
    expire_in: 1 week
  only:
    - main
    - master

# Deploy stage - Deploy to production
deploy:
  stage: deploy
  image: alpine:latest
  tags:
    - PROMODETECT
  before_script:
    - apk add --no-cache openssh-client curl docker-cli
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - echo "$SSH_KNOWN_HOSTS" >> ~/.ssh/known_hosts
    - chmod 644 ~/.ssh/known_hosts
  script:
    - echo "Starting deployment to production server..."
    - chmod +x ./scripts/deploy.sh
    - ./scripts/deploy.sh
  environment:
    name: production
    url: http://**************:8000
  when: manual
  only:
    - main
    - master
  artifacts:
    paths:
      - deployment.log
    expire_in: 1 week

# Post-deploy stage - Health checks and notifications
health-check:
  stage: post-deploy
  image: alpine:latest
  tags:
    - PROMODETECT
  before_script:
    - apk add --no-cache curl jq
  script:
    - echo "Performing health checks..."
    - chmod +x ./scripts/health-check.sh
    - ./scripts/health-check.sh
  dependencies:
    - deploy
  only:
    - main
    - master
  when: on_success

# Rollback job (manual trigger)
rollback:
  stage: deploy
  image: alpine:latest
  tags:
    - PROMODETECT
  before_script:
    - apk add --no-cache openssh-client docker-cli
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - echo "$SSH_KNOWN_HOSTS" >> ~/.ssh/known_hosts
    - chmod 644 ~/.ssh/known_hosts
  script:
    - echo "Rolling back to previous version..."
    - chmod +x ./scripts/rollback.sh
    - ./scripts/rollback.sh
  environment:
    name: production
    url: http://**************:8000
  when: manual
  only:
    - main
    - master

# Cleanup old images
cleanup:
  stage: post-deploy
  image: alpine:latest
  tags:
    - PROMODETECT
  before_script:
    - apk add --no-cache openssh-client
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - echo "$SSH_KNOWN_HOSTS" >> ~/.ssh/known_hosts
    - chmod 644 ~/.ssh/known_hosts
  script:
    - echo "Cleaning up old Docker images..."
    - ssh $DEPLOY_USER@************** "docker image prune -f --filter 'until=72h'"
    - ssh $DEPLOY_USER@************** "docker system prune -f"
  when: on_success
  only:
    - main
    - master
