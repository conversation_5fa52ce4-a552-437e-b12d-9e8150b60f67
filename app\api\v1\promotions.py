"""
Promotion management endpoints.
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.deps import get_current_user, get_current_merchant_user, get_current_admin_user, get_optional_current_user
from app.schemas.promotion import (
    PromotionCreate, PromotionUpdate, PromotionResponse, PromotionList, 
    PromotionSearch, PromotionStats, PromotionSummary
)
from app.services.promotion_service import PromotionService
from app.models.user import User
from app.models.promotion import PromotionType, PromotionStatus

router = APIRouter()


@router.get("/", response_model=PromotionList)
async def list_promotions(
    page: int = Query(1, ge=1),
    per_page: int = Query(20, ge=1, le=100),
    query: Optional[str] = Query(None),
    category_id: Optional[int] = Query(None),
    merchant_id: Optional[int] = Query(None),
    promotion_type: Optional[PromotionType] = Query(None),
    is_featured: Optional[bool] = Query(None),
    is_exclusive: Optional[bool] = Query(None),
    min_discount: Optional[float] = Query(None, ge=0),
    max_discount: Optional[float] = Query(None, ge=0),
    location_lat: Optional[float] = Query(None, ge=-90, le=90),
    location_lng: Optional[float] = Query(None, ge=-180, le=180),
    radius_km: Optional[float] = Query(None, gt=0),
    sort_by: str = Query("created_at", regex="^(created_at|start_date|end_date|view_count|favorite_count|discount_value)$"),
    sort_order: str = Query("desc", regex="^(asc|desc)$"),
    current_user: Optional[User] = Depends(get_optional_current_user),
    db: Session = Depends(get_db)
):
    """
    List promotions with search and filtering.
    """
    promotion_service = PromotionService(db)
    
    search_params = PromotionSearch(
        query=query,
        category_id=category_id,
        merchant_id=merchant_id,
        promotion_type=promotion_type,
        status=PromotionStatus.ACTIVE,  # Only show active promotions to public
        is_featured=is_featured,
        is_exclusive=is_exclusive,
        min_discount=min_discount,
        max_discount=max_discount,
        location_lat=location_lat,
        location_lng=location_lng,
        radius_km=radius_km,
        page=page,
        per_page=per_page,
        sort_by=sort_by,
        sort_order=sort_order
    )
    
    promotions, total = promotion_service.search_promotions(search_params)
    pages = (total + per_page - 1) // per_page
    
    return PromotionList(
        promotions=promotions,
        total=total,
        page=page,
        per_page=per_page,
        pages=pages
    )


@router.get("/{promotion_id}", response_model=PromotionResponse)
async def get_promotion(
    promotion_id: int,
    current_user: Optional[User] = Depends(get_optional_current_user),
    db: Session = Depends(get_db)
):
    """
    Get promotion details by ID.
    """
    promotion_service = PromotionService(db)
    promotion = promotion_service.get_or_404(promotion_id)
    
    # Increment view count
    promotion_service.increment_view_count(promotion_id)
    
    return promotion


@router.post("/", response_model=PromotionResponse, status_code=status.HTTP_201_CREATED)
async def create_promotion(
    promotion_data: PromotionCreate,
    current_user: User = Depends(get_current_merchant_user),
    db: Session = Depends(get_db)
):
    """
    Create a new promotion (Merchant only).
    """
    promotion_service = PromotionService(db)
    
    # Get merchant ID from user's merchant profile
    if not current_user.merchant_profile:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User does not have a merchant profile"
        )
    
    promotion = promotion_service.create_promotion(promotion_data, current_user.merchant_profile.id)
    return promotion


@router.put("/{promotion_id}", response_model=PromotionResponse)
async def update_promotion(
    promotion_id: int,
    promotion_data: PromotionUpdate,
    current_user: User = Depends(get_current_merchant_user),
    db: Session = Depends(get_db)
):
    """
    Update a promotion (Merchant only - own promotions).
    """
    promotion_service = PromotionService(db)
    
    # Get merchant ID from user's merchant profile
    if not current_user.merchant_profile:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User does not have a merchant profile"
        )
    
    promotion = promotion_service.update_promotion(
        promotion_id, 
        promotion_data, 
        current_user.merchant_profile.id
    )
    return promotion


@router.delete("/{promotion_id}")
async def delete_promotion(
    promotion_id: int,
    current_user: User = Depends(get_current_merchant_user),
    db: Session = Depends(get_db)
):
    """
    Delete a promotion (Merchant only - own promotions).
    """
    promotion_service = PromotionService(db)
    
    # Get the promotion first to check ownership
    promotion = promotion_service.get_or_404(promotion_id)
    
    if not current_user.merchant_profile or promotion.merchant_id != current_user.merchant_profile.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to delete this promotion"
        )
    
    success = promotion_service.delete(promotion_id)
    
    if success:
        return {"message": "Promotion deleted successfully"}
    else:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Promotion not found"
        )


@router.post("/{promotion_id}/click")
async def track_promotion_click(
    promotion_id: int,
    current_user: Optional[User] = Depends(get_optional_current_user),
    db: Session = Depends(get_db)
):
    """
    Track promotion click for analytics.
    """
    promotion_service = PromotionService(db)
    
    # Verify promotion exists
    promotion_service.get_or_404(promotion_id)
    
    # Increment click count
    success = promotion_service.increment_click_count(promotion_id)
    
    if success:
        return {"message": "Click tracked successfully"}
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Failed to track click"
        )


# Merchant-specific endpoints
@router.get("/merchant/my-promotions", response_model=PromotionList)
async def get_my_promotions(
    page: int = Query(1, ge=1),
    per_page: int = Query(20, ge=1, le=100),
    status: Optional[PromotionStatus] = Query(None),
    current_user: User = Depends(get_current_merchant_user),
    db: Session = Depends(get_db)
):
    """
    Get current merchant's promotions.
    """
    promotion_service = PromotionService(db)
    
    if not current_user.merchant_profile:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User does not have a merchant profile"
        )
    
    search_params = PromotionSearch(
        merchant_id=current_user.merchant_profile.id,
        status=status,
        page=page,
        per_page=per_page
    )
    
    promotions, total = promotion_service.search_promotions(search_params)
    pages = (total + per_page - 1) // per_page
    
    return PromotionList(
        promotions=promotions,
        total=total,
        page=page,
        per_page=per_page,
        pages=pages
    )


@router.get("/merchant/stats", response_model=PromotionStats)
async def get_merchant_promotion_stats(
    current_user: User = Depends(get_current_merchant_user),
    db: Session = Depends(get_db)
):
    """
    Get promotion statistics for current merchant.
    """
    promotion_service = PromotionService(db)
    
    if not current_user.merchant_profile:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User does not have a merchant profile"
        )
    
    stats = promotion_service.get_promotion_stats(current_user.merchant_profile.id)
    return stats


# Admin endpoints
@router.get("/admin/all", response_model=PromotionList)
async def admin_list_all_promotions(
    page: int = Query(1, ge=1),
    per_page: int = Query(20, ge=1, le=100),
    status: Optional[PromotionStatus] = Query(None),
    merchant_id: Optional[int] = Query(None),
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    List all promotions (Admin only).
    """
    promotion_service = PromotionService(db)
    
    search_params = PromotionSearch(
        merchant_id=merchant_id,
        status=status,
        page=page,
        per_page=per_page
    )
    
    promotions, total = promotion_service.search_promotions(search_params)
    pages = (total + per_page - 1) // per_page
    
    return PromotionList(
        promotions=promotions,
        total=total,
        page=page,
        per_page=per_page,
        pages=pages
    )


@router.get("/admin/stats", response_model=PromotionStats)
async def admin_get_global_stats(
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    Get global promotion statistics (Admin only).
    """
    promotion_service = PromotionService(db)
    stats = promotion_service.get_promotion_stats()
    return stats
