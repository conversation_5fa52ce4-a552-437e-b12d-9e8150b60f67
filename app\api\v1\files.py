"""
File upload endpoints.
"""
from typing import List
from fastapi import APIRouter, Depends, UploadFile, File, HTTPException, status
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
import os

from app.core.database import get_db
from app.core.deps import get_current_user, get_current_merchant_user
from app.services.file_service import FileService
from app.services.user_service import UserService
from app.models.user import User
from app.core.config import settings

router = APIRouter()


@router.post("/upload/avatar")
async def upload_avatar(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Upload user avatar image.
    """
    file_service = FileService()
    user_service = UserService(db)
    
    # Upload and resize avatar
    file_path = await file_service.upload_image(
        file, 
        folder="avatars",
        resize=(200, 200),
        quality=90
    )
    
    # Update user avatar URL
    avatar_url = file_service.get_file_url(file_path)
    user_service.update_user(current_user.id, {"avatar_url": avatar_url})
    
    return {
        "message": "Avatar uploaded successfully",
        "avatar_url": avatar_url,
        "file_path": file_path
    }


@router.post("/upload/promotion-image")
async def upload_promotion_image(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_merchant_user)
):
    """
    Upload promotion image (Merchant only).
    """
    file_service = FileService()
    
    # Upload and resize promotion image
    file_path = await file_service.upload_image(
        file,
        folder="promotions",
        resize=(800, 600),
        quality=85
    )
    
    # Create thumbnail
    thumbnail_path = file_service.create_thumbnail(file_path, size=(300, 225))
    
    return {
        "message": "Promotion image uploaded successfully",
        "image_url": file_service.get_file_url(file_path),
        "thumbnail_url": file_service.get_file_url(thumbnail_path),
        "file_path": file_path,
        "thumbnail_path": thumbnail_path
    }


@router.post("/upload/promotion-gallery")
async def upload_promotion_gallery(
    files: List[UploadFile] = File(...),
    current_user: User = Depends(get_current_merchant_user)
):
    """
    Upload multiple images for promotion gallery (Merchant only).
    """
    file_service = FileService()
    
    # Upload multiple images
    file_paths = await file_service.upload_multiple_images(
        files,
        folder="promotions",
        max_files=5,
        resize=(800, 600)
    )
    
    # Create thumbnails for each image
    gallery_data = []
    for file_path in file_paths:
        thumbnail_path = file_service.create_thumbnail(file_path, size=(300, 225))
        gallery_data.append({
            "image_url": file_service.get_file_url(file_path),
            "thumbnail_url": file_service.get_file_url(thumbnail_path),
            "file_path": file_path,
            "thumbnail_path": thumbnail_path
        })
    
    return {
        "message": f"{len(file_paths)} images uploaded successfully",
        "gallery": gallery_data
    }


@router.post("/upload/merchant-logo")
async def upload_merchant_logo(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_merchant_user),
    db: Session = Depends(get_db)
):
    """
    Upload merchant logo (Merchant only).
    """
    file_service = FileService()
    
    # Check if user has merchant profile
    if not current_user.merchant_profile:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User does not have a merchant profile"
        )
    
    # Upload and resize logo
    file_path = await file_service.upload_image(
        file,
        folder="merchants",
        resize=(300, 300),
        quality=90
    )
    
    # Update merchant logo URL
    logo_url = file_service.get_file_url(file_path)
    current_user.merchant_profile.logo_url = logo_url
    db.commit()
    
    return {
        "message": "Merchant logo uploaded successfully",
        "logo_url": logo_url,
        "file_path": file_path
    }


@router.post("/upload/merchant-cover")
async def upload_merchant_cover(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_merchant_user),
    db: Session = Depends(get_db)
):
    """
    Upload merchant cover image (Merchant only).
    """
    file_service = FileService()
    
    # Check if user has merchant profile
    if not current_user.merchant_profile:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User does not have a merchant profile"
        )
    
    # Upload and resize cover image
    file_path = await file_service.upload_image(
        file,
        folder="merchants",
        resize=(1200, 400),
        quality=85
    )
    
    # Update merchant cover image URL
    cover_url = file_service.get_file_url(file_path)
    current_user.merchant_profile.cover_image_url = cover_url
    db.commit()
    
    return {
        "message": "Merchant cover image uploaded successfully",
        "cover_url": cover_url,
        "file_path": file_path
    }


@router.delete("/delete/{file_path:path}")
async def delete_file(
    file_path: str,
    current_user: User = Depends(get_current_user)
):
    """
    Delete uploaded file.
    """
    file_service = FileService()
    
    # Basic security check - only allow deletion of files in allowed folders
    allowed_folders = ["avatars", "promotions", "merchants"]
    folder = file_path.split("/")[0] if "/" in file_path else ""
    
    if folder not in allowed_folders:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to delete this file"
        )
    
    # Additional authorization checks could be added here
    # For example, check if the file belongs to the current user
    
    success = file_service.delete_file(file_path)
    
    if success:
        return {"message": "File deleted successfully"}
    else:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="File not found"
        )


# Serve uploaded files (in production, use nginx or CDN)
@router.get("/serve/{file_path:path}")
async def serve_file(file_path: str):
    """
    Serve uploaded files.
    Note: In production, this should be handled by nginx or a CDN.
    """
    full_path = os.path.join(settings.UPLOAD_DIR, file_path)
    
    if not os.path.exists(full_path):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="File not found"
        )
    
    return FileResponse(full_path)
