"""
Database initialization script.
"""
import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import Session
from app.core.database import engine, SessionLocal
from app.models import *
from app.core.security import get_password_hash
from app.models.user import User, UserRole, UserStatus
from app.models.category import Category
from app.models.merchant import Merchant, BusinessType, MerchantStatus
from app.models.notification import NotificationTemplate, NotificationType, NotificationPriority


def create_admin_user(db: Session):
    """Create default admin user."""
    admin_email = "<EMAIL>"
    
    # Check if admin already exists
    existing_admin = db.query(User).filter(User.email == admin_email).first()
    if existing_admin:
        print(f"Admin user already exists: {admin_email}")
        return existing_admin
    
    admin_user = User(
        email=admin_email,
        username="admin",
        hashed_password=get_password_hash("admin123"),
        first_name="System",
        last_name="Administrator",
        role=UserRole.ADMIN,
        status=UserStatus.ACTIVE,
        is_verified=True,
        email_verified=True
    )
    
    db.add(admin_user)
    db.commit()
    db.refresh(admin_user)
    
    print(f"Created admin user: {admin_email}")
    return admin_user


def create_default_categories(db: Session):
    """Create default promotion categories."""
    categories = [
        {
            "name": "Food & Dining",
            "slug": "food-dining",
            "description": "Restaurants, cafes, and food delivery",
            "name_en": "Food & Dining",
            "name_fr": "Nourriture et Restauration",
            "name_ar": "الطعام والمطاعم",
            "icon_url": "🍽️",
            "color_code": "#FF6B6B"
        },
        {
            "name": "Shopping",
            "slug": "shopping",
            "description": "Retail stores and online shopping",
            "name_en": "Shopping",
            "name_fr": "Shopping",
            "name_ar": "التسوق",
            "icon_url": "🛍️",
            "color_code": "#4ECDC4"
        },
        {
            "name": "Electronics",
            "slug": "electronics",
            "description": "Gadgets, computers, and electronic devices",
            "name_en": "Electronics",
            "name_fr": "Électronique",
            "name_ar": "الإلكترونيات",
            "icon_url": "📱",
            "color_code": "#45B7D1"
        },
        {
            "name": "Fashion",
            "slug": "fashion",
            "description": "Clothing, shoes, and accessories",
            "name_en": "Fashion",
            "name_fr": "Mode",
            "name_ar": "الموضة",
            "icon_url": "👗",
            "color_code": "#F7DC6F"
        },
        {
            "name": "Health & Beauty",
            "slug": "health-beauty",
            "description": "Healthcare, cosmetics, and wellness",
            "name_en": "Health & Beauty",
            "name_fr": "Santé et Beauté",
            "name_ar": "الصحة والجمال",
            "icon_url": "💄",
            "color_code": "#BB8FCE"
        },
        {
            "name": "Travel",
            "slug": "travel",
            "description": "Hotels, flights, and travel packages",
            "name_en": "Travel",
            "name_fr": "Voyage",
            "name_ar": "السفر",
            "icon_url": "✈️",
            "color_code": "#58D68D"
        },
        {
            "name": "Entertainment",
            "slug": "entertainment",
            "description": "Movies, games, and entertainment",
            "name_en": "Entertainment",
            "name_fr": "Divertissement",
            "name_ar": "الترفيه",
            "icon_url": "🎬",
            "color_code": "#F1948A"
        },
        {
            "name": "Services",
            "slug": "services",
            "description": "Professional and personal services",
            "name_en": "Services",
            "name_fr": "Services",
            "name_ar": "الخدمات",
            "icon_url": "🔧",
            "color_code": "#85C1E9"
        }
    ]
    
    created_categories = []
    for cat_data in categories:
        # Check if category already exists
        existing_cat = db.query(Category).filter(Category.slug == cat_data["slug"]).first()
        if existing_cat:
            print(f"Category already exists: {cat_data['name']}")
            created_categories.append(existing_cat)
            continue
        
        category = Category(**cat_data)
        db.add(category)
        created_categories.append(category)
    
    db.commit()
    
    for category in created_categories:
        db.refresh(category)
    
    print(f"Created {len([c for c in created_categories if c.id])} categories")
    return created_categories


def create_notification_templates(db: Session):
    """Create default notification templates."""
    templates = [
        {
            "name": "welcome_user",
            "notification_type": NotificationType.ACCOUNT_UPDATE,
            "title_template": "Welcome to Promodetect!",
            "message_template": "Welcome {user_name}! Start discovering amazing deals and promotions.",
            "title_template_en": "Welcome to Promodetect!",
            "title_template_fr": "Bienvenue sur Promodetect!",
            "title_template_ar": "مرحباً بك في بروموديتكت!",
            "message_template_en": "Welcome {user_name}! Start discovering amazing deals and promotions.",
            "message_template_fr": "Bienvenue {user_name}! Commencez à découvrir des offres et promotions incroyables.",
            "message_template_ar": "مرحباً {user_name}! ابدأ في اكتشاف العروض والترويجات المذهلة.",
            "default_channels": "in_app,email",
            "default_priority": NotificationPriority.NORMAL
        },
        {
            "name": "new_promotion_alert",
            "notification_type": NotificationType.PROMOTION_ALERT,
            "title_template": "New Promotion Available!",
            "message_template": "Check out this new promotion: {promotion_title}",
            "title_template_en": "New Promotion Available!",
            "title_template_fr": "Nouvelle Promotion Disponible!",
            "title_template_ar": "عرض ترويجي جديد متاح!",
            "message_template_en": "Check out this new promotion: {promotion_title}",
            "message_template_fr": "Découvrez cette nouvelle promotion: {promotion_title}",
            "message_template_ar": "اطلع على هذا العرض الترويجي الجديد: {promotion_title}",
            "default_channels": "in_app,push",
            "default_priority": NotificationPriority.NORMAL
        },
        {
            "name": "promotion_expiring",
            "notification_type": NotificationType.REMINDER,
            "title_template": "Promotion Expiring Soon!",
            "message_template": "Don't miss out! {promotion_title} expires in {days_left} days.",
            "title_template_en": "Promotion Expiring Soon!",
            "title_template_fr": "Promotion Expire Bientôt!",
            "title_template_ar": "العرض الترويجي ينتهي قريباً!",
            "message_template_en": "Don't miss out! {promotion_title} expires in {days_left} days.",
            "message_template_fr": "Ne ratez pas! {promotion_title} expire dans {days_left} jours.",
            "message_template_ar": "لا تفوت الفرصة! {promotion_title} ينتهي خلال {days_left} أيام.",
            "default_channels": "in_app,email,push",
            "default_priority": NotificationPriority.HIGH
        }
    ]
    
    created_templates = []
    for template_data in templates:
        # Check if template already exists
        existing_template = db.query(NotificationTemplate).filter(
            NotificationTemplate.name == template_data["name"]
        ).first()
        if existing_template:
            print(f"Notification template already exists: {template_data['name']}")
            created_templates.append(existing_template)
            continue
        
        template = NotificationTemplate(**template_data)
        db.add(template)
        created_templates.append(template)
    
    db.commit()
    
    for template in created_templates:
        db.refresh(template)
    
    print(f"Created {len([t for t in created_templates if t.id])} notification templates")
    return created_templates


def main():
    """Initialize the database with default data."""
    print("Initializing database...")
    
    # Create all tables
    Base.metadata.create_all(bind=engine)
    print("Database tables created")
    
    # Create session
    db = SessionLocal()
    
    try:
        # Create default data
        admin_user = create_admin_user(db)
        categories = create_default_categories(db)
        templates = create_notification_templates(db)
        
        print("\nDatabase initialization completed successfully!")
        print(f"Admin user: {admin_user.email}")
        print(f"Categories created: {len(categories)}")
        print(f"Notification templates created: {len(templates)}")
        
    except Exception as e:
        print(f"Error during database initialization: {e}")
        db.rollback()
        raise
    finally:
        db.close()


if __name__ == "__main__":
    main()
