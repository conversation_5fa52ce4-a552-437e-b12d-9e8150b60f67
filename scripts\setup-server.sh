#!/bin/bash

# Promodetect API Server Setup Script
# This script sets up the production server for deployment

set -e

# Configuration
DEPLOY_USER="deploy"
APP_DIR="/opt/promodetect"
DOCKER_COMPOSE_VERSION="2.21.0"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
    exit 1
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Function to check if running as root
check_root() {
    if [ "$EUID" -ne 0 ]; then
        error "This script must be run as root (use sudo)"
    fi
}

# Function to update system
update_system() {
    log "Updating system packages..."
    apt update && apt upgrade -y
    apt install -y curl wget git unzip software-properties-common apt-transport-https ca-certificates gnupg lsb-release
    success "System updated successfully"
}

# Function to install Docker
install_docker() {
    log "Installing Docker..."
    
    # Remove old versions
    apt remove -y docker docker-engine docker.io containerd runc || true
    
    # Add Docker's official GPG key
    curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
    
    # Add Docker repository
    echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null
    
    # Install Docker
    apt update
    apt install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin
    
    # Start and enable Docker
    systemctl start docker
    systemctl enable docker
    
    # Test Docker installation
    docker --version
    success "Docker installed successfully"
}

# Function to install Docker Compose
install_docker_compose() {
    log "Installing Docker Compose..."
    
    # Download Docker Compose
    curl -L "https://github.com/docker/compose/releases/download/v${DOCKER_COMPOSE_VERSION}/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    
    # Make it executable
    chmod +x /usr/local/bin/docker-compose
    
    # Create symlink
    ln -sf /usr/local/bin/docker-compose /usr/bin/docker-compose
    
    # Test installation
    docker-compose --version
    success "Docker Compose installed successfully"
}

# Function to create deploy user
create_deploy_user() {
    log "Creating deploy user..."
    
    # Create user if it doesn't exist
    if ! id "$DEPLOY_USER" &>/dev/null; then
        useradd -m -s /bin/bash "$DEPLOY_USER"
        success "Deploy user created"
    else
        warning "Deploy user already exists"
    fi
    
    # Add to sudo group
    usermod -aG sudo "$DEPLOY_USER"
    
    # Add to docker group
    usermod -aG docker "$DEPLOY_USER"
    
    # Create SSH directory
    mkdir -p /home/<USER>/.ssh
    chown $DEPLOY_USER:$DEPLOY_USER /home/<USER>/.ssh
    chmod 700 /home/<USER>/.ssh
    
    success "Deploy user configured"
}

# Function to setup application directory
setup_app_directory() {
    log "Setting up application directory..."
    
    # Create application directory
    mkdir -p $APP_DIR/{config,logs,uploads,backups,ssl}
    
    # Create subdirectories for uploads
    mkdir -p $APP_DIR/uploads/{images,avatars,promotions,merchants}
    
    # Set ownership
    chown -R $DEPLOY_USER:$DEPLOY_USER $APP_DIR
    
    # Set permissions
    chmod -R 755 $APP_DIR
    chmod 700 $APP_DIR/config
    
    success "Application directory created"
}

# Function to configure firewall
configure_firewall() {
    log "Configuring firewall..."
    
    # Install ufw if not installed
    apt install -y ufw
    
    # Reset firewall rules
    ufw --force reset
    
    # Default policies
    ufw default deny incoming
    ufw default allow outgoing
    
    # Allow SSH
    ufw allow ssh
    ufw allow 22/tcp
    
    # Allow HTTP and HTTPS
    ufw allow 80/tcp
    ufw allow 443/tcp
    
    # Allow from database server
    ufw allow from **************
    
    # Enable firewall
    ufw --force enable
    
    success "Firewall configured"
}

# Function to install GitLab Runner
install_gitlab_runner() {
    log "Installing GitLab Runner..."
    
    # Add GitLab Runner repository
    curl -L "https://packages.gitlab.com/install/repositories/runner/gitlab-runner/script.deb.sh" | bash
    
    # Install GitLab Runner
    apt install -y gitlab-runner
    
    # Add gitlab-runner to docker group
    usermod -aG docker gitlab-runner
    
    success "GitLab Runner installed"
    warning "Remember to register the runner with: sudo gitlab-runner register"
}

# Function to install monitoring tools
install_monitoring_tools() {
    log "Installing monitoring tools..."
    
    # Install htop, iotop, and other monitoring tools
    apt install -y htop iotop nethogs ncdu tree jq bc
    
    # Install log rotation
    apt install -y logrotate
    
    # Create logrotate configuration for application logs
    cat > /etc/logrotate.d/promodetect << 'EOF'
/opt/promodetect/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 deploy deploy
    postrotate
        docker kill -s USR1 promodetect-api-app 2>/dev/null || true
    endscript
}
EOF
    
    success "Monitoring tools installed"
}

# Function to configure system limits
configure_system_limits() {
    log "Configuring system limits..."
    
    # Increase file descriptor limits
    cat >> /etc/security/limits.conf << 'EOF'
# Promodetect API limits
deploy soft nofile 65536
deploy hard nofile 65536
gitlab-runner soft nofile 65536
gitlab-runner hard nofile 65536
EOF
    
    # Configure sysctl for better performance
    cat >> /etc/sysctl.conf << 'EOF'
# Promodetect API optimizations
net.core.somaxconn = 65535
net.ipv4.tcp_max_syn_backlog = 65535
net.ipv4.ip_local_port_range = 1024 65535
net.ipv4.tcp_fin_timeout = 30
vm.swappiness = 10
EOF
    
    # Apply sysctl changes
    sysctl -p
    
    success "System limits configured"
}

# Function to setup SSL certificates (Let's Encrypt)
setup_ssl_certificates() {
    log "Setting up SSL certificate support..."
    
    # Install certbot
    apt install -y certbot
    
    # Create SSL directory
    mkdir -p $APP_DIR/ssl
    chown $DEPLOY_USER:$DEPLOY_USER $APP_DIR/ssl
    
    success "SSL certificate support configured"
    warning "Run 'certbot certonly --standalone -d your-domain.com' to get SSL certificates"
}

# Function to create backup script
create_backup_script() {
    log "Creating backup script..."
    
    cat > /usr/local/bin/promodetect-backup << 'EOF'
#!/bin/bash

# Promodetect API Backup Script
BACKUP_DIR="/opt/promodetect/backups"
DATE=$(date +%Y%m%d_%H%M%S)
DB_HOST="**************"
DB_NAME="promodetect_db"
DB_USER="promodetect_user"

# Create backup directory
mkdir -p $BACKUP_DIR

# Backup database
PGPASSWORD="$DATABASE_PASSWORD" pg_dump -h $DB_HOST -U $DB_USER $DB_NAME > $BACKUP_DIR/db_backup_$DATE.sql

# Backup uploads
tar -czf $BACKUP_DIR/uploads_backup_$DATE.tar.gz -C /opt/promodetect uploads/

# Backup configuration
tar -czf $BACKUP_DIR/config_backup_$DATE.tar.gz -C /opt/promodetect config/

# Remove backups older than 7 days
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete

echo "Backup completed: $DATE"
EOF
    
    chmod +x /usr/local/bin/promodetect-backup
    
    # Create cron job for daily backups
    echo "0 2 * * * $DEPLOY_USER /usr/local/bin/promodetect-backup" >> /etc/crontab
    
    success "Backup script created"
}

# Function to create health check script
create_health_check_script() {
    log "Creating health check script..."
    
    cat > /usr/local/bin/promodetect-health << 'EOF'
#!/bin/bash

# Promodetect API Health Check Script
API_URL="http://localhost"
LOG_FILE="/opt/promodetect/logs/health-check.log"

# Function to log with timestamp
log_message() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1" >> $LOG_FILE
}

# Check API health
if curl -s "$API_URL/health" | grep -q "healthy"; then
    log_message "API health check: PASSED"
else
    log_message "API health check: FAILED"
    # Send alert (implement notification logic here)
fi

# Check disk space
DISK_USAGE=$(df /opt/promodetect | awk 'NR==2 {print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    log_message "Disk usage warning: ${DISK_USAGE}%"
fi

# Check memory usage
MEMORY_USAGE=$(free | awk 'NR==2{printf "%.2f", $3*100/$2}')
if (( $(echo "$MEMORY_USAGE > 80" | bc -l) )); then
    log_message "Memory usage warning: ${MEMORY_USAGE}%"
fi

# Check Docker containers
if ! docker ps | grep -q "promodetect-api-app"; then
    log_message "API container is not running"
fi
EOF
    
    chmod +x /usr/local/bin/promodetect-health
    
    # Create cron job for health checks every 5 minutes
    echo "*/5 * * * * $DEPLOY_USER /usr/local/bin/promodetect-health" >> /etc/crontab
    
    success "Health check script created"
}

# Function to display setup summary
display_summary() {
    log "Server setup completed successfully!"
    echo ""
    echo "=== Setup Summary ==="
    echo "✅ System updated"
    echo "✅ Docker installed"
    echo "✅ Docker Compose installed"
    echo "✅ Deploy user created: $DEPLOY_USER"
    echo "✅ Application directory: $APP_DIR"
    echo "✅ Firewall configured"
    echo "✅ GitLab Runner installed"
    echo "✅ Monitoring tools installed"
    echo "✅ System limits configured"
    echo "✅ SSL support configured"
    echo "✅ Backup script created"
    echo "✅ Health check script created"
    echo ""
    echo "=== Next Steps ==="
    echo "1. Register GitLab Runner:"
    echo "   sudo gitlab-runner register"
    echo ""
    echo "2. Add SSH public key for deploy user:"
    echo "   sudo nano /home/<USER>/.ssh/authorized_keys"
    echo ""
    echo "3. Configure SSL certificates (optional):"
    echo "   certbot certonly --standalone -d your-domain.com"
    echo ""
    echo "4. Test Docker access:"
    echo "   sudo -u $DEPLOY_USER docker ps"
    echo ""
    echo "5. Set up database connection from this server to **************"
    echo ""
    success "Server is ready for Promodetect API deployment!"
}

# Main setup function
main() {
    log "Starting Promodetect API server setup..."
    
    # Check if running as root
    check_root
    
    # Perform setup steps
    update_system
    install_docker
    install_docker_compose
    create_deploy_user
    setup_app_directory
    configure_firewall
    install_gitlab_runner
    install_monitoring_tools
    configure_system_limits
    setup_ssl_certificates
    create_backup_script
    create_health_check_script
    
    # Display summary
    display_summary
}

# Run setup
main "$@"
