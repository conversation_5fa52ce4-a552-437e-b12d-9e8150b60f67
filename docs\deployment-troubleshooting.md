# Deployment Troubleshooting Guide

This guide helps troubleshoot common issues with the Promodetect API deployment pipeline.

## 🚨 Common Pipeline Issues

### 1. Build Stage Failures

#### Docker Build Fails
```bash
# Error: Docker build context too large
# Solution: Add .dockerignore file
echo "node_modules
.git
.pytest_cache
__pycache__
*.pyc
.coverage
htmlcov/
.env
logs/
uploads/" > .dockerignore
```

#### Docker Registry Login Issues
```bash
# Check GitLab CI/CD variables
# Ensure CI_REGISTRY_PASSWORD and CI_REGISTRY_USER are set
# Verify GitLab Container Registry is enabled
```

### 2. Test Stage Failures

#### Database Connection Issues
```yaml
# In .gitlab-ci.yml, ensure correct service configuration
services:
  - postgres:15
  - redis:7-alpine
variables:
  POSTGRES_DB: test_promodetect_db
  POSTGRES_USER: test_user
  POSTGRES_PASSWORD: test_password
```

#### Missing Dependencies
```bash
# Add missing system dependencies to .gitlab-ci.yml
before_script:
  - apt-get update -qy
  - apt-get install -y gcc libpq-dev build-essential
```

### 3. Deploy Stage Failures

#### SSH Connection Issues
```bash
# Test SSH connection manually
ssh -i ~/.ssh/deploy_key deploy@**************

# Check SSH key format in GitLab variables
# Ensure SSH_PRIVATE_KEY includes header/footer:
-----BEGIN OPENSSH PRIVATE KEY-----
...key content...
-----END OPENSSH PRIVATE KEY-----
```

#### Docker Pull Issues
```bash
# On server, test Docker registry access
docker login registry.gitlab.com
docker pull $CI_REGISTRY_IMAGE:latest

# Check if GitLab Runner has Docker access
sudo -u gitlab-runner docker ps
```

## 🔧 Server-Side Troubleshooting

### 1. Container Issues

#### Check Container Status
```bash
# List all containers
docker ps -a

# Check specific container logs
docker logs promodetect-api-app
docker logs promodetect-api-nginx
docker logs promodetect-api-redis

# Check container health
docker inspect promodetect-api-app --format='{{.State.Health.Status}}'
```

#### Container Won't Start
```bash
# Check environment file
cat /opt/promodetect/config/.env

# Test container manually
docker run --rm -it \
  --env-file /opt/promodetect/config/.env \
  $CI_REGISTRY_IMAGE:latest \
  python -c "import app; print('OK')"

# Check port conflicts
netstat -tulpn | grep :8000
```

### 2. Database Connection Issues

#### Test Database Connectivity
```bash
# From application server
psql -h ************** -U promodetect_user -d promodetect_db

# Test from container
docker exec promodetect-api-app python -c "
from app.core.database import engine
try:
    with engine.connect() as conn:
        result = conn.execute('SELECT 1')
        print('Database connection successful')
except Exception as e:
    print(f'Database connection failed: {e}')
"
```

#### Database Server Issues
```bash
# On database server (**************)
sudo systemctl status postgresql
sudo -u postgres psql -c "SELECT version();"

# Check PostgreSQL logs
sudo tail -f /var/log/postgresql/postgresql-15-main.log

# Check connections
sudo -u postgres psql -c "SELECT * FROM pg_stat_activity;"
```

### 3. Network Issues

#### Check Firewall Rules
```bash
# On application server
sudo ufw status verbose

# Test connectivity to database
telnet ************** 5432
nc -zv ************** 5432
```

#### Check DNS Resolution
```bash
# Test DNS resolution
nslookup **************
ping **************
```

### 4. Performance Issues

#### Check Resource Usage
```bash
# CPU and memory usage
htop
free -h
df -h

# Docker resource usage
docker stats

# Check disk I/O
iotop
```

#### Application Performance
```bash
# Check application logs for slow queries
docker logs promodetect-api-app | grep -i "slow\|timeout"

# Monitor API response times
curl -w "@curl-format.txt" -o /dev/null -s "http://localhost/health"

# Create curl-format.txt:
echo "     time_namelookup:  %{time_namelookup}
        time_connect:  %{time_connect}
     time_appconnect:  %{time_appconnect}
    time_pretransfer:  %{time_pretransfer}
       time_redirect:  %{time_redirect}
  time_starttransfer:  %{time_starttransfer}
                     ----------
          time_total:  %{time_total}" > curl-format.txt
```

## 🔍 Debugging Commands

### Application Debugging
```bash
# Enter application container
docker exec -it promodetect-api-app bash

# Check Python environment
docker exec promodetect-api-app python -c "
import sys
print('Python version:', sys.version)
import app
print('App imported successfully')
"

# Run database migrations manually
docker exec promodetect-api-app alembic current
docker exec promodetect-api-app alembic upgrade head

# Test API endpoints
curl -X GET "http://localhost/health"
curl -X GET "http://localhost/api/v1/info"
```

### Database Debugging
```bash
# Check database schema
docker exec promodetect-api-app python -c "
from app.core.database import engine
from sqlalchemy import inspect
inspector = inspect(engine)
print('Tables:', inspector.get_table_names())
"

# Run SQL queries
docker exec promodetect-api-app python -c "
from app.core.database import SessionLocal
db = SessionLocal()
result = db.execute('SELECT COUNT(*) FROM users')
print('User count:', result.scalar())
db.close()
"
```

### Log Analysis
```bash
# Application logs
tail -f /opt/promodetect/logs/app.log

# System logs
journalctl -u docker -f
journalctl -u gitlab-runner -f

# Nginx access logs
docker logs promodetect-api-nginx | tail -100

# Filter error logs
docker logs promodetect-api-app 2>&1 | grep -i error
```

## 🚑 Emergency Procedures

### 1. Quick Rollback
```bash
# Manual rollback using backup image
cd /opt/promodetect
docker stop promodetect-api-app
docker rm promodetect-api-app

# Find latest backup
docker images | grep backup

# Start with backup image
docker run -d \
  --name promodetect-api-app \
  --env-file ./config/.env \
  --volume ./uploads:/app/uploads \
  --volume ./logs:/app/logs \
  --network promodetect_promodetect-network \
  --restart unless-stopped \
  promodetect-api:backup-YYYYMMDD-HHMMSS
```

### 2. Database Recovery
```bash
# Restore from backup
PGPASSWORD="password" psql -h ************** -U promodetect_user -d promodetect_db < backup.sql

# Reset database if corrupted
docker exec promodetect-api-app python -c "
from app.core.database import Base, engine
Base.metadata.drop_all(engine)
Base.metadata.create_all(engine)
"
```

### 3. Complete Service Restart
```bash
# Stop all services
cd /opt/promodetect
docker-compose -f docker-compose.prod.yml down

# Clean up
docker system prune -f

# Restart services
docker-compose -f docker-compose.prod.yml up -d

# Check status
docker-compose -f docker-compose.prod.yml ps
```

## 📊 Monitoring and Alerts

### Health Check Script
```bash
#!/bin/bash
# /usr/local/bin/health-monitor.sh

API_URL="http://localhost"
ALERT_EMAIL="<EMAIL>"

# Check API health
if ! curl -s "$API_URL/health" | grep -q "healthy"; then
    echo "API health check failed" | mail -s "Promodetect API Alert" $ALERT_EMAIL
fi

# Check disk space
DISK_USAGE=$(df /opt/promodetect | awk 'NR==2 {print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 90 ]; then
    echo "Disk usage critical: ${DISK_USAGE}%" | mail -s "Disk Space Alert" $ALERT_EMAIL
fi

# Check memory usage
MEMORY_USAGE=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
if [ $MEMORY_USAGE -gt 90 ]; then
    echo "Memory usage critical: ${MEMORY_USAGE}%" | mail -s "Memory Alert" $ALERT_EMAIL
fi
```

### Log Monitoring
```bash
# Monitor for errors in real-time
tail -f /opt/promodetect/logs/app.log | grep -i "error\|exception\|critical"

# Set up log rotation
sudo logrotate -f /etc/logrotate.d/promodetect
```

## 📞 Escalation Procedures

### Level 1: Automatic Recovery
- Health checks detect issues
- Automatic container restart
- Alert notifications sent

### Level 2: Manual Intervention
- SSH to server and investigate
- Check logs and container status
- Apply fixes or restart services

### Level 3: Rollback
- Use GitLab CI/CD rollback job
- Or manual rollback to previous version
- Investigate root cause

### Level 4: Emergency Response
- Contact on-call engineer
- Consider maintenance mode
- Coordinate with team for resolution

## 📋 Maintenance Checklist

### Daily
- [ ] Check application health
- [ ] Review error logs
- [ ] Monitor resource usage
- [ ] Verify backup completion

### Weekly
- [ ] Update system packages
- [ ] Clean up old Docker images
- [ ] Review security logs
- [ ] Test backup restoration

### Monthly
- [ ] Security audit
- [ ] Performance review
- [ ] Update dependencies
- [ ] Disaster recovery test

## 🔗 Useful Resources

- **GitLab CI/CD Documentation**: https://docs.gitlab.com/ee/ci/
- **Docker Documentation**: https://docs.docker.com/
- **FastAPI Documentation**: https://fastapi.tiangolo.com/
- **PostgreSQL Documentation**: https://www.postgresql.org/docs/
- **Nginx Documentation**: https://nginx.org/en/docs/
