"""
Base service class for common CRUD operations.
"""
from typing import TypeVar, Generic, Type, Optional, List, Any, Dict
from sqlalchemy.orm import Session
from sqlalchemy import and_
from fastapi import HTTPException, status

from app.models.base import BaseModel

ModelType = TypeVar("ModelType", bound=BaseModel)


class BaseService(Generic[ModelType]):
    """Base service class with common CRUD operations."""
    
    def __init__(self, model: Type[ModelType], db: Session):
        self.model = model
        self.db = db
    
    def get_by_id(self, id: int) -> Optional[ModelType]:
        """
        Get entity by ID.
        
        Args:
            id: Entity ID
            
        Returns:
            Entity or None if not found
        """
        return self.db.query(self.model).filter(
            and_(self.model.id == id, self.model.is_active == True)
        ).first()
    
    def get_all(self, skip: int = 0, limit: int = 100) -> List[ModelType]:
        """
        Get all entities with pagination.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of entities
        """
        return self.db.query(self.model).filter(
            self.model.is_active == True
        ).offset(skip).limit(limit).all()
    
    def create(self, **kwargs) -> ModelType:
        """
        Create a new entity.
        
        Args:
            **kwargs: Entity data
            
        Returns:
            Created entity
        """
        entity = self.model(**kwargs)
        self.db.add(entity)
        self.db.commit()
        self.db.refresh(entity)
        return entity
    
    def update(self, id: int, **kwargs) -> Optional[ModelType]:
        """
        Update an entity.
        
        Args:
            id: Entity ID
            **kwargs: Updated data
            
        Returns:
            Updated entity or None if not found
        """
        entity = self.get_by_id(id)
        if not entity:
            return None
        
        for key, value in kwargs.items():
            if hasattr(entity, key):
                setattr(entity, key, value)
        
        self.db.commit()
        self.db.refresh(entity)
        return entity
    
    def delete(self, id: int) -> bool:
        """
        Soft delete an entity.
        
        Args:
            id: Entity ID
            
        Returns:
            True if deleted, False if not found
        """
        entity = self.get_by_id(id)
        if not entity:
            return False
        
        entity.is_active = False
        self.db.commit()
        return True
    
    def hard_delete(self, id: int) -> bool:
        """
        Hard delete an entity.
        
        Args:
            id: Entity ID
            
        Returns:
            True if deleted, False if not found
        """
        entity = self.get_by_id(id)
        if not entity:
            return False
        
        self.db.delete(entity)
        self.db.commit()
        return True
    
    def count(self, **filters) -> int:
        """
        Count entities with optional filters.
        
        Args:
            **filters: Filter conditions
            
        Returns:
            Count of entities
        """
        query = self.db.query(self.model).filter(self.model.is_active == True)
        
        for key, value in filters.items():
            if hasattr(self.model, key):
                query = query.filter(getattr(self.model, key) == value)
        
        return query.count()
    
    def exists(self, id: int) -> bool:
        """
        Check if entity exists.
        
        Args:
            id: Entity ID
            
        Returns:
            True if exists, False otherwise
        """
        return self.db.query(self.model).filter(
            and_(self.model.id == id, self.model.is_active == True)
        ).first() is not None
    
    def get_or_404(self, id: int) -> ModelType:
        """
        Get entity by ID or raise 404 error.
        
        Args:
            id: Entity ID
            
        Returns:
            Entity
            
        Raises:
            HTTPException: If entity not found
        """
        entity = self.get_by_id(id)
        if not entity:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"{self.model.__name__} not found"
            )
        return entity
    
    def bulk_create(self, entities_data: List[Dict[str, Any]]) -> List[ModelType]:
        """
        Create multiple entities.
        
        Args:
            entities_data: List of entity data dictionaries
            
        Returns:
            List of created entities
        """
        entities = [self.model(**data) for data in entities_data]
        self.db.add_all(entities)
        self.db.commit()
        
        for entity in entities:
            self.db.refresh(entity)
        
        return entities
    
    def bulk_update(self, updates: List[Dict[str, Any]]) -> List[ModelType]:
        """
        Update multiple entities.
        
        Args:
            updates: List of update dictionaries with 'id' and update data
            
        Returns:
            List of updated entities
        """
        updated_entities = []
        
        for update_data in updates:
            entity_id = update_data.pop('id')
            entity = self.update(entity_id, **update_data)
            if entity:
                updated_entities.append(entity)
        
        return updated_entities
