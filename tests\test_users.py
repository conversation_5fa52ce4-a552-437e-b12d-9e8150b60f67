"""
Tests for user management endpoints.
"""
import pytest
from fastapi.testclient import Test<PERSON><PERSON>


def test_get_user_profile(client: Test<PERSON><PERSON>, auth_headers, test_user):
    """Test getting user profile."""
    response = client.get("/api/v1/users/profile", headers=auth_headers)
    assert response.status_code == 200
    
    data = response.json()
    assert data["id"] == test_user.id
    assert data["username"] == test_user.username
    assert data["first_name"] == test_user.first_name


def test_update_user_profile(client: TestClient, auth_headers):
    """Test updating user profile."""
    update_data = {
        "first_name": "Updated",
        "last_name": "Name",
        "bio": "Updated bio",
        "city": "Updated City"
    }
    
    response = client.put("/api/v1/users/profile", json=update_data, headers=auth_headers)
    assert response.status_code == 200
    
    data = response.json()
    assert data["first_name"] == update_data["first_name"]
    assert data["last_name"] == update_data["last_name"]
    assert data["bio"] == update_data["bio"]
    assert data["city"] == update_data["city"]


def test_change_password_success(client: TestClient, auth_headers):
    """Test successful password change."""
    password_data = {
        "current_password": "testpassword",
        "new_password": "NewPassword123!",
        "confirm_new_password": "NewPassword123!"
    }
    
    response = client.post("/api/v1/users/change-password", json=password_data, headers=auth_headers)
    assert response.status_code == 200
    assert "Password changed successfully" in response.json()["message"]


def test_change_password_wrong_current(client: TestClient, auth_headers):
    """Test password change with wrong current password."""
    password_data = {
        "current_password": "wrongpassword",
        "new_password": "NewPassword123!",
        "confirm_new_password": "NewPassword123!"
    }
    
    response = client.post("/api/v1/users/change-password", json=password_data, headers=auth_headers)
    assert response.status_code == 400
    assert "Current password is incorrect" in response.json()["detail"]


def test_change_password_mismatch(client: TestClient, auth_headers):
    """Test password change with password mismatch."""
    password_data = {
        "current_password": "testpassword",
        "new_password": "NewPassword123!",
        "confirm_new_password": "DifferentPassword123!"
    }
    
    response = client.post("/api/v1/users/change-password", json=password_data, headers=auth_headers)
    assert response.status_code == 422


def test_get_user_by_id(client: TestClient, test_user):
    """Test getting user by ID (public profile)."""
    response = client.get(f"/api/v1/users/{test_user.id}")
    assert response.status_code == 200
    
    data = response.json()
    assert data["id"] == test_user.id
    assert data["username"] == test_user.username
    # Should not include sensitive information
    assert "email" not in data


def test_get_user_by_id_not_found(client: TestClient):
    """Test getting non-existent user."""
    response = client.get("/api/v1/users/99999")
    assert response.status_code == 404


def test_list_users_admin_only(client: TestClient, admin_headers):
    """Test listing users (admin only)."""
    response = client.get("/api/v1/users/", headers=admin_headers)
    assert response.status_code == 200
    
    data = response.json()
    assert "users" in data
    assert "total" in data
    assert "page" in data
    assert "per_page" in data


def test_list_users_unauthorized(client: TestClient, auth_headers):
    """Test listing users without admin privileges."""
    response = client.get("/api/v1/users/", headers=auth_headers)
    assert response.status_code == 403


def test_get_user_details_admin(client: TestClient, admin_headers, test_user):
    """Test getting full user details (admin only)."""
    response = client.get(f"/api/v1/users/admin/{test_user.id}", headers=admin_headers)
    assert response.status_code == 200
    
    data = response.json()
    assert data["id"] == test_user.id
    assert data["email"] == test_user.email
    assert data["username"] == test_user.username


def test_update_user_admin(client: TestClient, admin_headers, test_user):
    """Test updating user as admin."""
    update_data = {
        "first_name": "Admin Updated",
        "last_name": "Name"
    }
    
    response = client.put(f"/api/v1/users/admin/{test_user.id}", json=update_data, headers=admin_headers)
    assert response.status_code == 200
    
    data = response.json()
    assert data["first_name"] == update_data["first_name"]
    assert data["last_name"] == update_data["last_name"]


def test_delete_user_admin(client: TestClient, admin_headers, test_user):
    """Test deleting user as admin."""
    response = client.delete(f"/api/v1/users/admin/{test_user.id}", headers=admin_headers)
    assert response.status_code == 200
    assert "User deleted successfully" in response.json()["message"]


def test_verify_user_admin(client: TestClient, admin_headers, test_user):
    """Test verifying user as admin."""
    response = client.post(f"/api/v1/users/admin/{test_user.id}/verify", headers=admin_headers)
    assert response.status_code == 200
    assert "User verified successfully" in response.json()["message"]


def test_user_search_with_filters(client: TestClient, admin_headers):
    """Test user search with filters."""
    params = {
        "query": "test",
        "role": "customer",
        "page": 1,
        "per_page": 10
    }
    
    response = client.get("/api/v1/users/", params=params, headers=admin_headers)
    assert response.status_code == 200
    
    data = response.json()
    assert "users" in data
    assert data["page"] == 1
    assert data["per_page"] == 10
