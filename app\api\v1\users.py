"""
User management endpoints.
"""
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.deps import get_current_user, get_current_admin_user
from app.schemas.user import (
    UserResponse, UserUpdate, UserList, UserSearch, 
    PasswordChange, UserProfile
)
from app.services.user_service import UserService
from app.models.user import User

router = APIRouter()


@router.get("/profile", response_model=UserProfile)
async def get_user_profile(
    current_user: User = Depends(get_current_user)
):
    """
    Get current user profile.
    """
    return current_user


@router.put("/profile", response_model=UserResponse)
async def update_user_profile(
    user_data: UserUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Update current user profile.
    """
    user_service = UserService(db)
    updated_user = user_service.update_user(current_user.id, user_data)
    return updated_user


@router.post("/change-password")
async def change_password(
    password_data: Password<PERSON>hange,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Change user password.
    """
    user_service = UserService(db)
    success = user_service.change_password(
        current_user.id,
        password_data.current_password,
        password_data.new_password
    )
    
    if success:
        return {"message": "Password changed successfully"}
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Failed to change password"
        )


@router.get("/{user_id}", response_model=UserProfile)
async def get_user_by_id(
    user_id: int,
    db: Session = Depends(get_db)
):
    """
    Get user profile by ID (public information only).
    """
    user_service = UserService(db)
    user = user_service.get_by_id(user_id)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    return user


# Admin endpoints
@router.get("/", response_model=UserList)
async def list_users(
    page: int = Query(1, ge=1),
    per_page: int = Query(20, ge=1, le=100),
    query: str = Query(None),
    role: str = Query(None),
    status: str = Query(None),
    country: str = Query(None),
    city: str = Query(None),
    is_verified: bool = Query(None),
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    List users with search and filtering (Admin only).
    """
    user_service = UserService(db)
    
    search_params = UserSearch(
        query=query,
        role=role,
        status=status,
        country=country,
        city=city,
        is_verified=is_verified,
        page=page,
        per_page=per_page
    )
    
    users, total = user_service.search_users(search_params)
    pages = (total + per_page - 1) // per_page
    
    return UserList(
        users=users,
        total=total,
        page=page,
        per_page=per_page,
        pages=pages
    )


@router.get("/admin/{user_id}", response_model=UserResponse)
async def get_user_details(
    user_id: int,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    Get full user details (Admin only).
    """
    user_service = UserService(db)
    user = user_service.get_or_404(user_id)
    return user


@router.put("/admin/{user_id}", response_model=UserResponse)
async def update_user(
    user_id: int,
    user_data: UserUpdate,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    Update user (Admin only).
    """
    user_service = UserService(db)
    updated_user = user_service.update_user(user_id, user_data)
    return updated_user


@router.delete("/admin/{user_id}")
async def delete_user(
    user_id: int,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    Delete user (Admin only).
    """
    user_service = UserService(db)
    success = user_service.delete(user_id)
    
    if success:
        return {"message": "User deleted successfully"}
    else:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )


@router.post("/admin/{user_id}/verify")
async def verify_user(
    user_id: int,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    Verify user email (Admin only).
    """
    user_service = UserService(db)
    user = user_service.verify_email(user_id)
    return {"message": "User verified successfully", "user": user}
