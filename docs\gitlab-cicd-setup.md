# GitLab CI/CD Setup Guide for Promodetect API

This document provides detailed instructions for setting up the GitLab CI/CD pipeline for the Promodetect API project.

## Required GitLab CI/CD Variables

Configure the following variables in your GitLab project settings under **Settings > CI/CD > Variables**. Mark sensitive variables as **Masked** and **Protected**.

### 🔐 Database Configuration (Masked & Protected)

| Variable Name | Description | Example Value | Masked | Protected |
|---------------|-------------|---------------|---------|-----------|
| `DATABASE_NAME` | PostgreSQL database name | `promodetect_db` | ✅ | ✅ |
| `DATABASE_USER` | PostgreSQL username | `promodetect_user` | ✅ | ✅ |
| `DATABASE_PASSWORD` | PostgreSQL password | `secure_db_password_123` | ✅ | ✅ |

### 🔑 JWT & Security Configuration (Masked & Protected)

| Variable Name | Description | Example Value | Masked | Protected |
|---------------|-------------|---------------|---------|-----------|
| `JWT_SECRET_KEY` | JWT signing secret key | `super-secret-jwt-key-256-bits-long` | ✅ | ✅ |

### 🚀 Deployment Configuration (Masked & Protected)

| Variable Name | Description | Example Value | Masked | Protected |
|---------------|-------------|---------------|---------|-----------|
| `DEPLOY_USER` | SSH username for deployment | `deploy` | ✅ | ✅ |
| `SSH_PRIVATE_KEY` | SSH private key for server access | `-----BEGIN OPENSSH PRIVATE KEY-----...` | ✅ | ✅ |
| `SSH_KNOWN_HOSTS` | SSH known hosts for server verification | `************** ssh-rsa AAAAB3...` | ❌ | ✅ |

### 📧 Email Configuration (Masked & Protected)

| Variable Name | Description | Example Value | Masked | Protected |
|---------------|-------------|---------------|---------|-----------|
| `MAIL_USERNAME` | SMTP username | `<EMAIL>` | ✅ | ✅ |
| `MAIL_PASSWORD` | SMTP password | `smtp_password_123` | ✅ | ✅ |
| `MAIL_FROM` | From email address | `<EMAIL>` | ❌ | ✅ |
| `MAIL_SERVER` | SMTP server | `smtp.gmail.com` | ❌ | ✅ |

### 🔔 Notification Configuration (Optional, Masked)

| Variable Name | Description | Example Value | Masked | Protected |
|---------------|-------------|---------------|---------|-----------|
| `SLACK_WEBHOOK_URL` | Slack webhook for notifications | `https://hooks.slack.com/services/...` | ✅ | ❌ |
| `DISCORD_WEBHOOK_URL` | Discord webhook for notifications | `https://discord.com/api/webhooks/...` | ✅ | ❌ |

## 🖥️ Server Setup Instructions

### Prerequisites

Before running the CI/CD pipeline, ensure the following setup on your servers:

#### Backend Server (**************)

1. **Install Docker and Docker Compose**
   ```bash
   # Update system
   sudo apt update && sudo apt upgrade -y
   
   # Install Docker
   curl -fsSL https://get.docker.com -o get-docker.sh
   sudo sh get-docker.sh
   
   # Install Docker Compose
   sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
   sudo chmod +x /usr/local/bin/docker-compose
   
   # Add deploy user to docker group
   sudo usermod -aG docker deploy
   ```

2. **Create Deploy User**
   ```bash
   # Create deploy user
   sudo useradd -m -s /bin/bash deploy
   sudo usermod -aG sudo deploy
   
   # Setup SSH directory
   sudo mkdir -p /home/<USER>/.ssh
   sudo chown deploy:deploy /home/<USER>/.ssh
   sudo chmod 700 /home/<USER>/.ssh
   ```

3. **Configure SSH Access**
   ```bash
   # Add your public key to authorized_keys
   sudo nano /home/<USER>/.ssh/authorized_keys
   sudo chown deploy:deploy /home/<USER>/.ssh/authorized_keys
   sudo chmod 600 /home/<USER>/.ssh/authorized_keys
   ```

4. **Create Application Directory**
   ```bash
   sudo mkdir -p /opt/promodetect
   sudo chown deploy:deploy /opt/promodetect
   ```

#### Database Server (**************)

1. **Install PostgreSQL**
   ```bash
   # Update system
   sudo apt update && sudo apt upgrade -y
   
   # Install PostgreSQL
   sudo apt install postgresql postgresql-contrib -y
   
   # Start and enable PostgreSQL
   sudo systemctl start postgresql
   sudo systemctl enable postgresql
   ```

2. **Configure PostgreSQL**
   ```bash
   # Switch to postgres user
   sudo -u postgres psql
   
   # Create database and user
   CREATE DATABASE promodetect_db;
   CREATE USER promodetect_user WITH PASSWORD 'secure_db_password_123';
   GRANT ALL PRIVILEGES ON DATABASE promodetect_db TO promodetect_user;
   ALTER USER promodetect_user CREATEDB;
   \q
   ```

3. **Configure PostgreSQL for Remote Access**
   ```bash
   # Edit postgresql.conf
   sudo nano /etc/postgresql/15/main/postgresql.conf
   # Add: listen_addresses = '*'
   
   # Edit pg_hba.conf
   sudo nano /etc/postgresql/15/main/pg_hba.conf
   # Add: host all all **************/32 md5
   
   # Restart PostgreSQL
   sudo systemctl restart postgresql
   ```

## 🔧 GitLab Runner Setup

### Install GitLab Runner on Backend Server

1. **Install GitLab Runner**
   ```bash
   # Download and install GitLab Runner
   curl -L "https://packages.gitlab.com/install/repositories/runner/gitlab-runner/script.deb.sh" | sudo bash
   sudo apt-get install gitlab-runner
   ```

2. **Register GitLab Runner**
   ```bash
   sudo gitlab-runner register
   ```
   
   Use the following configuration:
   - GitLab instance URL: `https://gitlab.com/`
   - Registration token: (Get from GitLab project settings)
   - Description: `Promodetect Production Runner`
   - Tags: `PROMODETECT`
   - Executor: `shell`

3. **Configure Runner User**
   ```bash
   # Add gitlab-runner to docker group
   sudo usermod -aG docker gitlab-runner
   
   # Test docker access
   sudo -u gitlab-runner docker ps
   ```

## 🚀 Deployment Process

### Manual Deployment Steps

1. **Trigger Pipeline**
   - Push code to `main` or `master` branch
   - Pipeline will automatically build and test
   - Manual approval required for deployment

2. **Monitor Deployment**
   - Check GitLab CI/CD pipeline status
   - Monitor deployment logs
   - Verify health checks

3. **Rollback if Needed**
   - Use the manual rollback job in GitLab CI/CD
   - Or run rollback script manually on server

### Environment Variables on Server

The deployment script will create a production `.env` file with the following structure:

```env
# Production Environment Configuration
DEBUG=False
ENVIRONMENT=production

# Database Configuration
DATABASE_HOST=**************
DATABASE_PORT=5432
DATABASE_NAME=promodetect_db
DATABASE_USER=promodetect_user
DATABASE_PASSWORD=secure_db_password_123
DATABASE_URL=************************************************************************/promodetect_db

# JWT Configuration
SECRET_KEY=super-secret-jwt-key-256-bits-long
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# API Configuration
API_V1_STR=/api/v1
PROJECT_NAME=Promodetect API
PROJECT_VERSION=1.0.0

# CORS Configuration
BACKEND_CORS_ORIGINS=["http://**************", "https://promodetect.com"]

# Redis Configuration
REDIS_URL=redis://redis:6379/0

# File Upload Configuration
UPLOAD_DIR=/app/uploads
MAX_FILE_SIZE=10485760
ALLOWED_IMAGE_EXTENSIONS=jpg,jpeg,png,gif,webp

# Email Configuration
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=smtp_password_123
MAIL_FROM=<EMAIL>
MAIL_PORT=587
MAIL_SERVER=smtp.gmail.com
MAIL_TLS=True
MAIL_SSL=False

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json

# Celery Configuration
CELERY_BROKER_URL=redis://redis:6379/1
CELERY_RESULT_BACKEND=redis://redis:6379/1

# Internationalization
DEFAULT_LANGUAGE=en
SUPPORTED_LANGUAGES=en,fr,ar
```

## 🔍 Monitoring and Troubleshooting

### Health Check Endpoints

- **Application Health**: `http://**************/health`
- **API Documentation**: `http://**************/api/v1/docs`
- **API Info**: `http://**************/api/v1/info`

### Log Locations

- **Application Logs**: `/opt/promodetect/logs/`
- **Docker Logs**: `docker logs promodetect-api-app`
- **Nginx Logs**: `docker logs promodetect-api-nginx`

### Common Issues and Solutions

1. **Database Connection Issues**
   ```bash
   # Test database connectivity
   docker exec promodetect-api-app python -c "
   import psycopg2
   conn = psycopg2.connect(
       host='**************',
       database='promodetect_db',
       user='promodetect_user',
       password='secure_db_password_123'
   )
   print('Database connection successful')
   "
   ```

2. **Container Issues**
   ```bash
   # Check container status
   docker ps -a
   
   # Check container logs
   docker logs promodetect-api-app
   
   # Restart containers
   cd /opt/promodetect
   docker-compose -f docker-compose.prod.yml restart
   ```

3. **Permission Issues**
   ```bash
   # Fix file permissions
   sudo chown -R deploy:deploy /opt/promodetect
   sudo chmod -R 755 /opt/promodetect
   ```

## 🔒 Security Considerations

1. **SSH Key Management**
   - Use dedicated SSH keys for deployment
   - Rotate keys regularly
   - Restrict SSH access to specific IPs

2. **Database Security**
   - Use strong passwords
   - Limit database access to application server only
   - Enable SSL connections

3. **Application Security**
   - Use strong JWT secret keys
   - Enable HTTPS in production
   - Regular security updates

4. **Network Security**
   - Configure firewall rules
   - Use VPN for server access
   - Monitor access logs

## 📞 Support and Maintenance

### Regular Maintenance Tasks

1. **Weekly**
   - Check application logs for errors
   - Monitor disk space usage
   - Review security logs

2. **Monthly**
   - Update system packages
   - Rotate log files
   - Review backup integrity

3. **Quarterly**
   - Security audit
   - Performance review
   - Dependency updates

### Emergency Contacts

- **DevOps Team**: <EMAIL>
- **On-call Engineer**: +1-XXX-XXX-XXXX
- **Slack Channel**: #promodetect-alerts
