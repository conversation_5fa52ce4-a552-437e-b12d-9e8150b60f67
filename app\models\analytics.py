"""
Analytics and tracking models.
"""
from sqlalchemy import Column, String, Text, Integer, Float, Boolean, Foreign<PERSON>ey, Enum, DateTime
from sqlalchemy.orm import relationship
import enum

from app.models.base import BaseModel


class EventType(str, enum.Enum):
    """Analytics event type enumeration."""
    PROMOTION_VIEW = "promotion_view"
    PROMOTION_CLICK = "promotion_click"
    PROMOTION_SHARE = "promotion_share"
    PROMOTION_FAVORITE = "promotion_favorite"
    PROMOTION_UNFAVORITE = "promotion_unfavorite"
    MERCHANT_VIEW = "merchant_view"
    MERCHANT_FOLLOW = "merchant_follow"
    SEARCH = "search"
    FILTER_APPLY = "filter_apply"
    USER_REGISTRATION = "user_registration"
    USER_LOGIN = "user_login"
    COUPON_COPY = "coupon_copy"
    EXTERNAL_REDIRECT = "external_redirect"


class DeviceType(str, enum.Enum):
    """Device type enumeration."""
    DESKTOP = "desktop"
    MOBILE = "mobile"
    TABLET = "tablet"
    UNKNOWN = "unknown"


class AnalyticsEvent(BaseModel):
    """Analytics event tracking."""
    __tablename__ = "analytics_events"
    
    # Event Information
    event_type = Column(Enum(EventType), nullable=False, index=True)
    event_name = Column(String(100), nullable=True)
    
    # User and Session
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    session_id = Column(String(100), nullable=True, index=True)
    
    # Related Entities
    promotion_id = Column(Integer, ForeignKey("promotions.id"), nullable=True)
    merchant_id = Column(Integer, ForeignKey("merchants.id"), nullable=True)
    category_id = Column(Integer, ForeignKey("categories.id"), nullable=True)
    
    # Event Data
    event_data = Column(Text, nullable=True)  # JSON string for additional event data
    
    # User Context
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(Text, nullable=True)
    device_type = Column(Enum(DeviceType), nullable=True)
    browser = Column(String(100), nullable=True)
    operating_system = Column(String(100), nullable=True)
    
    # Location Context
    country = Column(String(100), nullable=True)
    city = Column(String(100), nullable=True)
    latitude = Column(Float, nullable=True)
    longitude = Column(Float, nullable=True)
    
    # Referrer Information
    referrer_url = Column(String(1000), nullable=True)
    utm_source = Column(String(100), nullable=True)
    utm_medium = Column(String(100), nullable=True)
    utm_campaign = Column(String(100), nullable=True)
    utm_term = Column(String(100), nullable=True)
    utm_content = Column(String(100), nullable=True)
    
    # Relationships
    user = relationship("User", back_populates="analytics_events")
    promotion = relationship("Promotion", back_populates="analytics_events")
    merchant = relationship("Merchant")
    category = relationship("Category")


class DailyStats(BaseModel):
    """Daily aggregated statistics."""
    __tablename__ = "daily_stats"
    
    # Date
    date = Column(DateTime, nullable=False, index=True)
    
    # Entity
    entity_type = Column(String(50), nullable=False)  # promotion, merchant, category, global
    entity_id = Column(Integer, nullable=True)
    
    # Metrics
    views = Column(Integer, default=0, nullable=False)
    clicks = Column(Integer, default=0, nullable=False)
    conversions = Column(Integer, default=0, nullable=False)
    favorites = Column(Integer, default=0, nullable=False)
    shares = Column(Integer, default=0, nullable=False)
    
    # User Metrics
    unique_users = Column(Integer, default=0, nullable=False)
    new_users = Column(Integer, default=0, nullable=False)
    returning_users = Column(Integer, default=0, nullable=False)
    
    # Engagement Metrics
    avg_session_duration = Column(Float, default=0.0, nullable=False)
    bounce_rate = Column(Float, default=0.0, nullable=False)
    
    # Revenue Metrics (if applicable)
    revenue = Column(Float, default=0.0, nullable=False)
    avg_order_value = Column(Float, default=0.0, nullable=False)
