"""
Promotion schemas for request/response validation.
"""
from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field, validator

from app.models.promotion import PromotionType, PromotionStatus, DiscountType


class PromotionBase(BaseModel):
    """Base promotion schema."""
    title: str = Field(..., min_length=1, max_length=200)
    description: str = Field(..., min_length=1)
    short_description: Optional[str] = Field(None, max_length=500)
    category_id: int = Field(..., gt=0)
    location_id: Optional[int] = Field(None, gt=0)
    promotion_type: PromotionType
    discount_type: Optional[DiscountType] = None
    discount_value: Optional[float] = Field(None, ge=0)
    original_price: Optional[float] = Field(None, ge=0)
    discounted_price: Optional[float] = Field(None, ge=0)
    start_date: datetime
    end_date: datetime
    is_unlimited: bool = False
    max_uses: Optional[int] = Field(None, ge=1)
    max_uses_per_user: Optional[int] = Field(None, ge=1)
    is_featured: bool = False
    is_exclusive: bool = False
    requires_membership: bool = False
    terms_conditions: Optional[str] = None
    minimum_purchase: Optional[float] = Field(None, ge=0)
    coupon_code: Optional[str] = Field(None, max_length=50)
    auto_apply: bool = False
    
    @validator('end_date')
    def end_date_after_start_date(cls, v, values):
        if 'start_date' in values and v <= values['start_date']:
            raise ValueError('End date must be after start date')
        return v
    
    @validator('discounted_price')
    def discounted_price_validation(cls, v, values):
        if v is not None and 'original_price' in values and values['original_price'] is not None:
            if v >= values['original_price']:
                raise ValueError('Discounted price must be less than original price')
        return v


class PromotionCreate(PromotionBase):
    """Schema for creating a promotion."""
    pass


class PromotionUpdate(BaseModel):
    """Schema for updating a promotion."""
    title: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = Field(None, min_length=1)
    short_description: Optional[str] = Field(None, max_length=500)
    category_id: Optional[int] = Field(None, gt=0)
    location_id: Optional[int] = Field(None, gt=0)
    promotion_type: Optional[PromotionType] = None
    discount_type: Optional[DiscountType] = None
    discount_value: Optional[float] = Field(None, ge=0)
    original_price: Optional[float] = Field(None, ge=0)
    discounted_price: Optional[float] = Field(None, ge=0)
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    is_unlimited: Optional[bool] = None
    max_uses: Optional[int] = Field(None, ge=1)
    max_uses_per_user: Optional[int] = Field(None, ge=1)
    is_featured: Optional[bool] = None
    is_exclusive: Optional[bool] = None
    requires_membership: Optional[bool] = None
    terms_conditions: Optional[str] = None
    minimum_purchase: Optional[float] = Field(None, ge=0)
    coupon_code: Optional[str] = Field(None, max_length=50)
    auto_apply: Optional[bool] = None
    status: Optional[PromotionStatus] = None


class PromotionResponse(PromotionBase):
    """Schema for promotion response."""
    id: int
    merchant_id: int
    status: PromotionStatus
    current_uses: int
    image_url: Optional[str] = None
    gallery_urls: Optional[str] = None
    video_url: Optional[str] = None
    view_count: int
    click_count: int
    conversion_count: int
    favorite_count: int
    meta_title: Optional[str] = None
    meta_description: Optional[str] = None
    slug: Optional[str] = None
    source_url: Optional[str] = None
    source_name: Optional[str] = None
    external_id: Optional[str] = None
    last_scraped_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime
    is_active: bool
    
    # Multilingual fields
    title_en: Optional[str] = None
    title_fr: Optional[str] = None
    title_ar: Optional[str] = None
    description_en: Optional[str] = None
    description_fr: Optional[str] = None
    description_ar: Optional[str] = None
    
    class Config:
        from_attributes = True


class PromotionSummary(BaseModel):
    """Schema for promotion summary (list view)."""
    id: int
    title: str
    short_description: Optional[str] = None
    merchant_id: int
    category_id: int
    promotion_type: PromotionType
    discount_value: Optional[float] = None
    original_price: Optional[float] = None
    discounted_price: Optional[float] = None
    start_date: datetime
    end_date: datetime
    status: PromotionStatus
    is_featured: bool
    is_exclusive: bool
    image_url: Optional[str] = None
    view_count: int
    favorite_count: int
    created_at: datetime
    
    class Config:
        from_attributes = True


class PromotionList(BaseModel):
    """Schema for promotion list response."""
    promotions: List[PromotionSummary]
    total: int
    page: int
    per_page: int
    pages: int


class PromotionSearch(BaseModel):
    """Schema for promotion search."""
    query: Optional[str] = None
    category_id: Optional[int] = None
    merchant_id: Optional[int] = None
    promotion_type: Optional[PromotionType] = None
    status: Optional[PromotionStatus] = None
    is_featured: Optional[bool] = None
    is_exclusive: Optional[bool] = None
    min_discount: Optional[float] = Field(None, ge=0)
    max_discount: Optional[float] = Field(None, ge=0)
    location_lat: Optional[float] = Field(None, ge=-90, le=90)
    location_lng: Optional[float] = Field(None, ge=-180, le=180)
    radius_km: Optional[float] = Field(None, gt=0)
    start_date_from: Optional[datetime] = None
    start_date_to: Optional[datetime] = None
    end_date_from: Optional[datetime] = None
    end_date_to: Optional[datetime] = None
    page: int = Field(1, ge=1)
    per_page: int = Field(20, ge=1, le=100)
    sort_by: str = Field("created_at", regex="^(created_at|start_date|end_date|view_count|favorite_count|discount_value)$")
    sort_order: str = Field("desc", regex="^(asc|desc)$")


class PromotionStats(BaseModel):
    """Schema for promotion statistics."""
    total_promotions: int
    active_promotions: int
    expired_promotions: int
    draft_promotions: int
    total_views: int
    total_clicks: int
    total_conversions: int
    total_favorites: int
    avg_conversion_rate: float
