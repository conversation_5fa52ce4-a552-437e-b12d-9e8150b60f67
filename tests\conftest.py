"""
Pytest configuration and fixtures.
"""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from app.main import app
from app.core.database import get_db, Base
from app.core.security import get_password_hash
from app.models.user import User, UserRole, UserStatus
from app.models.category import Category
from app.models.merchant import Merchant, BusinessType, MerchantStatus

# Test database URL
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"

# Create test engine
engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)

# Create test session
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def override_get_db():
    """Override database dependency for testing."""
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()


# Override the dependency
app.dependency_overrides[get_db] = override_get_db


@pytest.fixture(scope="session")
def db_engine():
    """Create test database engine."""
    Base.metadata.create_all(bind=engine)
    yield engine
    Base.metadata.drop_all(bind=engine)


@pytest.fixture(scope="function")
def db_session(db_engine):
    """Create test database session."""
    connection = db_engine.connect()
    transaction = connection.begin()
    session = TestingSessionLocal(bind=connection)
    
    yield session
    
    session.close()
    transaction.rollback()
    connection.close()


@pytest.fixture(scope="function")
def client():
    """Create test client."""
    return TestClient(app)


@pytest.fixture
def test_user(db_session):
    """Create a test user."""
    user = User(
        email="<EMAIL>",
        username="testuser",
        hashed_password=get_password_hash("testpassword"),
        first_name="Test",
        last_name="User",
        role=UserRole.CUSTOMER,
        status=UserStatus.ACTIVE,
        is_verified=True,
        email_verified=True
    )
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user


@pytest.fixture
def test_admin(db_session):
    """Create a test admin user."""
    admin = User(
        email="<EMAIL>",
        username="admin",
        hashed_password=get_password_hash("adminpassword"),
        first_name="Admin",
        last_name="User",
        role=UserRole.ADMIN,
        status=UserStatus.ACTIVE,
        is_verified=True,
        email_verified=True
    )
    db_session.add(admin)
    db_session.commit()
    db_session.refresh(admin)
    return admin


@pytest.fixture
def test_merchant_user(db_session):
    """Create a test merchant user."""
    user = User(
        email="<EMAIL>",
        username="merchant",
        hashed_password=get_password_hash("merchantpassword"),
        first_name="Merchant",
        last_name="User",
        role=UserRole.MERCHANT,
        status=UserStatus.ACTIVE,
        is_verified=True,
        email_verified=True
    )
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    
    # Create merchant profile
    merchant = Merchant(
        user_id=user.id,
        business_name="Test Business",
        business_type=BusinessType.RESTAURANT,
        business_description="A test restaurant",
        business_address="123 Test Street",
        city="Test City",
        country="Test Country",
        status=MerchantStatus.ACTIVE,
        is_verified=True
    )
    db_session.add(merchant)
    db_session.commit()
    db_session.refresh(merchant)
    
    user.merchant_profile = merchant
    return user


@pytest.fixture
def test_category(db_session):
    """Create a test category."""
    category = Category(
        name="Test Category",
        slug="test-category",
        description="A test category",
        name_en="Test Category",
        name_fr="Catégorie de Test",
        name_ar="فئة اختبار"
    )
    db_session.add(category)
    db_session.commit()
    db_session.refresh(category)
    return category


@pytest.fixture
def auth_headers(client, test_user):
    """Get authentication headers for test user."""
    login_data = {
        "email": test_user.email,
        "password": "testpassword"
    }
    response = client.post("/api/v1/auth/login", json=login_data)
    token = response.json()["access_token"]
    return {"Authorization": f"Bearer {token}"}


@pytest.fixture
def admin_headers(client, test_admin):
    """Get authentication headers for admin user."""
    login_data = {
        "email": test_admin.email,
        "password": "adminpassword"
    }
    response = client.post("/api/v1/auth/login", json=login_data)
    token = response.json()["access_token"]
    return {"Authorization": f"Bearer {token}"}


@pytest.fixture
def merchant_headers(client, test_merchant_user):
    """Get authentication headers for merchant user."""
    login_data = {
        "email": test_merchant_user.email,
        "password": "merchantpassword"
    }
    response = client.post("/api/v1/auth/login", json=login_data)
    token = response.json()["access_token"]
    return {"Authorization": f"Bearer {token}"}
