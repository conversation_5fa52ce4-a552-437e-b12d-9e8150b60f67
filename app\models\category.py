"""
Category model for organizing promotions.
"""
from sqlalchemy import <PERSON>umn, String, Text, Integer, Foreign<PERSON>ey, <PERSON>olean
from sqlalchemy.orm import relationship

from app.models.base import BaseModel


class Category(BaseModel):
    """Category model for organizing promotions."""
    __tablename__ = "categories"
    
    # Basic Information
    name = Column(String(100), nullable=False, index=True)
    slug = Column(String(100), unique=True, nullable=False, index=True)
    description = Column(Text, nullable=True)
    
    # Hierarchy
    parent_id = Column(Integer, ForeignKey("categories.id"), nullable=True)
    level = Column(Integer, default=0, nullable=False)
    sort_order = Column(Integer, default=0, nullable=False)
    
    # Display
    icon_url = Column(String(500), nullable=True)
    color_code = Column(String(7), nullable=True)  # Hex color code
    is_featured = Column(Boolean, default=False, nullable=False)
    
    # Multilingual Support
    name_en = Column(String(100), nullable=True)
    name_fr = Column(String(100), nullable=True)
    name_ar = Column(String(100), nullable=True)
    description_en = Column(Text, nullable=True)
    description_fr = Column(Text, nullable=True)
    description_ar = Column(Text, nullable=True)
    
    # Statistics
    promotion_count = Column(Integer, default=0, nullable=False)
    
    # Relationships
    parent = relationship("Category", remote_side=[id], backref="children")
    promotions = relationship("Promotion", back_populates="category")
    
    def get_name(self, language: str = "en") -> str:
        """Get category name in specified language."""
        if language == "fr" and self.name_fr:
            return self.name_fr
        elif language == "ar" and self.name_ar:
            return self.name_ar
        elif language == "en" and self.name_en:
            return self.name_en
        return self.name
    
    def get_description(self, language: str = "en") -> str:
        """Get category description in specified language."""
        if language == "fr" and self.description_fr:
            return self.description_fr
        elif language == "ar" and self.description_ar:
            return self.description_ar
        elif language == "en" and self.description_en:
            return self.description_en
        return self.description or ""
