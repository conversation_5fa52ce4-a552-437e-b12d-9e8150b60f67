"""
Basic tests for the main application.
"""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from app.main import app
from app.core.database import get_db, Base

# Create test database
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def override_get_db():
    """Override database dependency for testing."""
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()


app.dependency_overrides[get_db] = override_get_db

# Create test client
client = TestClient(app)


@pytest.fixture(scope="module")
def setup_database():
    """Setup test database."""
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


def test_health_check():
    """Test health check endpoint."""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"
    assert "version" in data


def test_root_endpoint():
    """Test root endpoint."""
    response = client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert "message" in data
    assert "version" in data


def test_api_info():
    """Test API info endpoint."""
    response = client.get("/api/v1/info")
    assert response.status_code == 200
    data = response.json()
    assert data["name"] == "Promodetect API"
    assert "version" in data


def test_user_registration(setup_database):
    """Test user registration."""
    user_data = {
        "email": "<EMAIL>",
        "username": "testuser",
        "password": "TestPassword123!",
        "confirm_password": "TestPassword123!",
        "first_name": "Test",
        "last_name": "User"
    }
    
    response = client.post("/api/v1/auth/register", json=user_data)
    assert response.status_code == 201
    data = response.json()
    assert data["email"] == user_data["email"]
    assert data["username"] == user_data["username"]


def test_user_login(setup_database):
    """Test user login."""
    # First register a user
    user_data = {
        "email": "<EMAIL>",
        "username": "loginuser",
        "password": "TestPassword123!",
        "confirm_password": "TestPassword123!",
        "first_name": "Login",
        "last_name": "User"
    }
    
    client.post("/api/v1/auth/register", json=user_data)
    
    # Then try to login
    login_data = {
        "email": "<EMAIL>",
        "password": "TestPassword123!"
    }
    
    response = client.post("/api/v1/auth/login", json=login_data)
    assert response.status_code == 200
    data = response.json()
    assert "access_token" in data
    assert "refresh_token" in data
    assert data["token_type"] == "bearer"


def test_protected_endpoint_without_auth():
    """Test accessing protected endpoint without authentication."""
    response = client.get("/api/v1/auth/me")
    assert response.status_code == 401


def test_protected_endpoint_with_auth(setup_database):
    """Test accessing protected endpoint with authentication."""
    # Register and login
    user_data = {
        "email": "<EMAIL>",
        "username": "protecteduser",
        "password": "TestPassword123!",
        "confirm_password": "TestPassword123!",
        "first_name": "Protected",
        "last_name": "User"
    }
    
    client.post("/api/v1/auth/register", json=user_data)
    
    login_data = {
        "email": "<EMAIL>",
        "password": "TestPassword123!"
    }
    
    login_response = client.post("/api/v1/auth/login", json=login_data)
    token = login_response.json()["access_token"]
    
    # Access protected endpoint
    headers = {"Authorization": f"Bearer {token}"}
    response = client.get("/api/v1/auth/me", headers=headers)
    assert response.status_code == 200
    data = response.json()
    assert data["email"] == user_data["email"]


def test_promotions_list():
    """Test promotions list endpoint."""
    response = client.get("/api/v1/promotions/")
    assert response.status_code == 200
    data = response.json()
    assert "promotions" in data
    assert "total" in data
    assert "page" in data


def test_invalid_endpoint():
    """Test invalid endpoint returns 404."""
    response = client.get("/api/v1/invalid-endpoint")
    assert response.status_code == 404
