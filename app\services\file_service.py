"""
File upload and management service.
"""
import os
import uuid
import aiofiles
from typing import List, Optional
from fastapi import UploadFile, HTTPException, status
from PIL import Image
import io

from app.core.config import settings


class FileService:
    """Service for handling file uploads and management."""
    
    def __init__(self):
        self.upload_dir = settings.UPLOAD_DIR
        self.max_file_size = settings.MAX_FILE_SIZE
        self.allowed_extensions = settings.ALLOWED_IMAGE_EXTENSIONS
        
        # Create upload directory if it doesn't exist
        os.makedirs(self.upload_dir, exist_ok=True)
        os.makedirs(os.path.join(self.upload_dir, "images"), exist_ok=True)
        os.makedirs(os.path.join(self.upload_dir, "avatars"), exist_ok=True)
        os.makedirs(os.path.join(self.upload_dir, "promotions"), exist_ok=True)
        os.makedirs(os.path.join(self.upload_dir, "merchants"), exist_ok=True)
    
    async def upload_image(
        self, 
        file: UploadFile, 
        folder: str = "images",
        resize: Optional[tuple] = None,
        quality: int = 85
    ) -> str:
        """
        Upload and process an image file.
        
        Args:
            file: Uploaded file
            folder: Subfolder to store the file
            resize: Optional tuple (width, height) to resize image
            quality: JPEG quality (1-100)
            
        Returns:
            Relative file path
            
        Raises:
            HTTPException: If file validation fails
        """
        # Validate file
        self._validate_image_file(file)
        
        # Generate unique filename
        file_extension = self._get_file_extension(file.filename)
        filename = f"{uuid.uuid4()}{file_extension}"
        file_path = os.path.join(self.upload_dir, folder, filename)
        
        try:
            # Read file content
            content = await file.read()
            
            # Process image if needed
            if resize or quality < 100:
                content = self._process_image(content, resize, quality)
            
            # Save file
            async with aiofiles.open(file_path, 'wb') as f:
                await f.write(content)
            
            # Return relative path
            return f"{folder}/{filename}"
            
        except Exception as e:
            # Clean up file if it was created
            if os.path.exists(file_path):
                os.remove(file_path)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to upload file: {str(e)}"
            )
    
    async def upload_multiple_images(
        self, 
        files: List[UploadFile], 
        folder: str = "images",
        max_files: int = 10,
        resize: Optional[tuple] = None
    ) -> List[str]:
        """
        Upload multiple image files.
        
        Args:
            files: List of uploaded files
            folder: Subfolder to store files
            max_files: Maximum number of files allowed
            resize: Optional tuple (width, height) to resize images
            
        Returns:
            List of relative file paths
            
        Raises:
            HTTPException: If validation fails
        """
        if len(files) > max_files:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Maximum {max_files} files allowed"
            )
        
        uploaded_files = []
        
        try:
            for file in files:
                file_path = await self.upload_image(file, folder, resize)
                uploaded_files.append(file_path)
            
            return uploaded_files
            
        except Exception as e:
            # Clean up uploaded files on error
            for file_path in uploaded_files:
                full_path = os.path.join(self.upload_dir, file_path)
                if os.path.exists(full_path):
                    os.remove(full_path)
            raise e
    
    def delete_file(self, file_path: str) -> bool:
        """
        Delete a file.
        
        Args:
            file_path: Relative file path
            
        Returns:
            True if deleted successfully
        """
        try:
            full_path = os.path.join(self.upload_dir, file_path)
            if os.path.exists(full_path):
                os.remove(full_path)
                return True
            return False
        except Exception:
            return False
    
    def get_file_url(self, file_path: str) -> str:
        """
        Get full URL for a file.
        
        Args:
            file_path: Relative file path
            
        Returns:
            Full file URL
        """
        if not file_path:
            return ""
        
        # In production, this would be your CDN or static file server URL
        base_url = "http://localhost:8000"  # This should come from settings
        return f"{base_url}/uploads/{file_path}"
    
    def _validate_image_file(self, file: UploadFile) -> None:
        """
        Validate uploaded image file.
        
        Args:
            file: Uploaded file
            
        Raises:
            HTTPException: If validation fails
        """
        # Check file size
        if file.size and file.size > self.max_file_size:
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail=f"File size exceeds maximum allowed size of {self.max_file_size} bytes"
            )
        
        # Check file extension
        if not file.filename:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Filename is required"
            )
        
        file_extension = self._get_file_extension(file.filename).lower()
        if file_extension[1:] not in self.allowed_extensions:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"File type not allowed. Allowed types: {', '.join(self.allowed_extensions)}"
            )
        
        # Check content type
        if not file.content_type or not file.content_type.startswith('image/'):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="File must be an image"
            )
    
    def _get_file_extension(self, filename: str) -> str:
        """Get file extension from filename."""
        return os.path.splitext(filename)[1].lower()
    
    def _process_image(
        self, 
        content: bytes, 
        resize: Optional[tuple] = None, 
        quality: int = 85
    ) -> bytes:
        """
        Process image (resize, compress, etc.).
        
        Args:
            content: Image content bytes
            resize: Optional tuple (width, height)
            quality: JPEG quality
            
        Returns:
            Processed image bytes
        """
        try:
            # Open image
            image = Image.open(io.BytesIO(content))
            
            # Convert to RGB if necessary (for JPEG)
            if image.mode in ('RGBA', 'LA', 'P'):
                image = image.convert('RGB')
            
            # Resize if requested
            if resize:
                image = image.resize(resize, Image.Resampling.LANCZOS)
            
            # Save processed image
            output = io.BytesIO()
            image.save(output, format='JPEG', quality=quality, optimize=True)
            return output.getvalue()
            
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Failed to process image: {str(e)}"
            )
    
    def create_thumbnail(self, file_path: str, size: tuple = (300, 300)) -> str:
        """
        Create thumbnail for an image.
        
        Args:
            file_path: Original image path
            size: Thumbnail size (width, height)
            
        Returns:
            Thumbnail file path
        """
        try:
            full_path = os.path.join(self.upload_dir, file_path)
            
            if not os.path.exists(full_path):
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Original image not found"
                )
            
            # Generate thumbnail filename
            name, ext = os.path.splitext(file_path)
            thumbnail_path = f"{name}_thumb{ext}"
            thumbnail_full_path = os.path.join(self.upload_dir, thumbnail_path)
            
            # Create thumbnail
            with Image.open(full_path) as image:
                image.thumbnail(size, Image.Resampling.LANCZOS)
                image.save(thumbnail_full_path, quality=85, optimize=True)
            
            return thumbnail_path
            
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to create thumbnail: {str(e)}"
            )
